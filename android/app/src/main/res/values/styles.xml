<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="android:windowBackground">@color/splash_background</item>
        <item name="android:navigationBarColor">@android:color/white</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:windowTranslucentStatus">false</item>
    </style>

    <style name="SplashTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@color/splash_background</item>
        <item name="android:navigationBarColor">@android:color/white</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>

    <style name="Theme.AppCompat.Translucent">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation</item>
    </style>

</resources>