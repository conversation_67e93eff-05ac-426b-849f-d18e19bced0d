rootProject.name = '<PERSON><PERSON><PERSON><PERSON>'
apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesSettingsGradle(settings)

include ':app', ':react-native-code-push'

includeBuild('../node_modules/@react-native/gradle-plugin')

project(':react-native-code-push').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-code-push/android/app')