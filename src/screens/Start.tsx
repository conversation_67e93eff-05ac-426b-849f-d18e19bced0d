import React, { useEffect } from 'react';
import { ImageBackground, SafeAreaView, View } from 'react-native';
import { ActionButton } from '@features/ui/button';
import Heading from '../features/ui/shared/Heading';
import Text from '../features/ui/shared/Text';
import { usePersistedStore } from '../utils/store';
import tw from '../utils/tailwind';
import analytics from '@react-native-firebase/analytics';

const Start = () => {
  useEffect(() => {
    analytics().logEvent('start_screen_opened');
  }, []);

  const toLogin = () => {
    usePersistedStore.setState(prev => ({ ...prev, welcome: false }));
    analytics().logEvent('launch_to_login_tapped');
  };

  return (
    <ImageBackground
      style={tw`h-full w-full flex-1`}
      source={require('../assets/images/home-background.png')}>
      <SafeAreaView style={tw`w-full flex-1 items-center justify-end mb-20`}>
        <View style={tw`px-4`}>
          <Heading style={tw`text-5xl text-white text-center mb-5`}>
            {'Здравословно,\n Вкусно и БОХО с Анна'}
          </Heading>

          <Text
            style={tw`text-white font-body-semibold text-base text-center mb-10`}>
            Открийте колекция от разнообразни здравословни и растителни рецепти.
          </Text>
        </View>

        <ActionButton label="Да започваме →" onPress={toLogin} />
      </SafeAreaView>
    </ImageBackground>
  );
};

export default Start;
