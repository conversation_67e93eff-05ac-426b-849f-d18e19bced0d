import { useScrollToTop } from '@react-navigation/native';
import React, { useMemo } from 'react';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { ClipboardDocumentListIcon } from 'react-native-heroicons/outline';

import {
  useShoppingListActions,
  useShoppingListStore,
} from '@features/shopping-list/shopping-list.store';
import { theme } from '../../tailwind.config';
import CheckBox from '../components/Checkbox';
import KeyboardShift from '../components/KeyboardShift';
import AddOwnItemToShoppingList from '../features/shopping-list/AddOwnItemToShoppingList';
import Container from '../features/ui/shared/Container';
import Heading from '../features/ui/shared/Heading';
import Text from '../features/ui/shared/Text';
import { useDataStore } from '../utils/store';
import tw from '../utils/tailwind';
import {
  mergeIngredients,
  RecipeIngredients,
} from './screen-utils/shopping-list-utils';

export const ShoppingList = () => {
  const scrollViewRef = React.useRef(null);

  useScrollToTop(scrollViewRef);

  const system = useDataStore(state => state.system);
  const actions = useShoppingListActions();
  const shoppingList = useShoppingListStore(state => state.list);

  const recipes = useMemo(
    () =>
      Object.entries(shoppingList)
        .map(
          ([title, items]) =>
            [title, mergeIngredients(items, system)] as [
              string,
              RecipeIngredients,
            ],
        )
        .filter(([, ing]) => Object.keys(ing).length > 0)
        .sort(([a], [b]) =>
          a === 'Моите продукти' ? -1 : b === 'Моите продукти' ? 1 : 0,
        ),
    [shoppingList, system],
  );

  return (
    <KeyboardShift>
      <ScrollView
        ref={scrollViewRef}
        style={tw`bg-white`}
        contentContainerStyle={tw`bg-white pb-10`}
        showsVerticalScrollIndicator={false}
        alwaysBounceVertical={true}>
        <Container style={tw`py-4`}>
          {recipes.length > 0 ? (
            recipes.map(([recipeTitle, ingredients], lIdx) => (
              <View key={recipeTitle} style={tw.style(lIdx > 0 && 'mt-6')}>
                <Heading>{recipeTitle}</Heading>

                <View style={tw`mt-2`}>
                  {Object.entries(ingredients).map(
                    ([key, { metric, imperial, checked }]) => (
                      <TouchableOpacity
                        key={key}
                        onPress={() =>
                          actions.toggleIngredient(recipeTitle, key)
                        }>
                        <View style={tw`flex-row`}>
                          <CheckBox size="sm" selected={checked} />

                          <Text
                            style={[
                              tw`ml-2`,
                              tw.style(checked && 'line-through'),
                            ]}>
                            {system === 'metric' ? metric : imperial}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    ),
                  )}
                </View>
              </View>
            ))
          ) : (
            <View style={tw`items-center justify-center mt-10`}>
              <ClipboardDocumentListIcon
                size={30}
                color={theme.extend.colors.black}
              />

              <Text style={tw`text-center mt-2 text-sm max-w-60`}>
                Вашия списък {'\n'} е все още празен
              </Text>
            </View>
          )}
        </Container>
      </ScrollView>
      <AddOwnItemToShoppingList />
    </KeyboardShift>
  );
};
