import React, { useRef, useState } from 'react';
import {
  Dimensions,
  Image,
  FlatList as RNFlat<PERSON>ist,
  SafeAreaView,
  View,
} from 'react-native';

import { ActionButton } from '@features/ui/button';
import { useScreenSize } from '@utils/hooks';
import { usePersistedStore } from '../utils/store';
import tw from '../utils/tailwind';

const { width, height } = Dimensions.get('window');

const onboardingData = [
  {
    id: '1',
    image: require('../assets/images/onboarding1.png'),
  },
  {
    id: '2',
    image: require('../assets/images/onboarding2.png'),
  },
  {
    id: '3',
    image: require('../assets/images/onboarding3.png'),
  },
  {
    id: '4',
    image: require('../assets/images/onboarding4.png'),
  },
  {
    id: '5',
    image: require('../assets/images/onboarding5.png'),
  },
  {
    id: '6',
    image: require('../assets/images/onboarding6.png'),
  },
];

const Dot = ({ active }) => (
  <View
    style={[
      tw`h-2 w-2 rounded-full mx-1`,
      active ? tw`bg-primary` : tw`bg-gray-300`,
    ]}
  />
);

const Onboarding = () => {
  const { isSmallScreen } = useScreenSize();
  const setPersistedStore = usePersistedStore(state => state.setPersistedStore);

  const [activeIndex, setActiveIndex] = useState(0);

  const finishOnboarding = () => {
    setPersistedStore('hasSeenOnboarding', true);
  };

  const handleButtonPress = () => {
    if (activeIndex === 5) {
      finishOnboarding();
    } else {
      setActiveIndex(prevIndex => prevIndex + 1);
      flatListRef.current?.scrollToIndex({
        index: activeIndex + 1,
        animated: true,
      });
    }
  };

  const flatListRef = useRef<RNFlatList>(null);

  const renderItem = ({ item }) => (
    <View style={[tw`flex-1`, { width }]}>
      <Image
        source={item.image}
        style={{
          width: width,
          // Fully visible images on small screens,
          // small padding on larger screens
          height: isSmallScreen ? height + 120 : height + 32,
          resizeMode: 'cover',
        }}
      />
    </View>
  );

  return (
    <View style={tw`flex-1`}>
      <RNFlatList
        ref={flatListRef}
        data={onboardingData}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={event => {
          const newIndex = Math.round(
            event.nativeEvent.contentOffset.x / width,
          );
          setActiveIndex(newIndex);
        }}
      />

      <SafeAreaView style={tw`absolute bottom-0 left-0 right-0`}>
        <View
          style={tw`flex-row justify-center mb-8 bg-white rounded-full px-3 py-2 self-center`}>
          {onboardingData.map((_, index) => (
            <Dot key={index} active={index === activeIndex} />
          ))}
        </View>

        <View style={tw`px-6 mb-8`}>
          <ActionButton
            label={activeIndex === 5 ? 'Да започваме →' : 'Напред →'}
            onPress={handleButtonPress}
          />
        </View>
      </SafeAreaView>
    </View>
  );
};

export default Onboarding;
