import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import axios, { AxiosError } from 'axios';
import React from 'react';
import { useForm } from 'react-hook-form';
import { Alert, SafeAreaView, TouchableOpacity, View } from 'react-native';
import { ActionButton } from '@features/ui/button';
import Container from '../features/ui/shared/Container';
import Spacer from '../features/ui/shared/Spacer';
import Text from '../features/ui/shared/Text';
import TextInput from '../features/ui/shared/TextInput';
import { forgotPassword } from '../utils/auth-actions';
import tw from '../utils/tailwind';
import { isValidEmail } from '../utils/validation';
import Heading from '../features/ui/shared/Heading';

interface ForgottenPasswordForm {
  email: string;
}

const ForgottenPassword = ({ navigation }) => {
  const {
    control,
    handleSubmit,
    formState: { isSubmitting },
  } = useForm<ForgottenPasswordForm>();

  const submit = async ({ email }) => {
    try {
      await forgotPassword({ email });

      Alert.alert('Изпратихме линк за ресет на паролата на мейла ви.');
    } catch (err) {
      if (!axios.isAxiosError(err)) {
        console.error(err);
      }

      // Handle axios errors
      const axiosError = err as AxiosError;

      if (axiosError.response?.status === 422) {
        Alert.alert('Грешен имейл или парола.');
        return;
      }

      console.error(axiosError.message);
    }
  };

  return (
    <KeyboardAwareScrollView
      bounces={false}
      enableOnAndroid
      extraScrollHeight={20}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={tw`bg-background min-h-full pb-20`}>
      <SafeAreaView>
        <Container style={tw`w-full items-start`}>
          <Heading style={tw`mt-4 text-4xl`}>Забравена парола</Heading>
          <Text style={tw`mt-2 mb-6 text-sm text-black`}>
            Въведете email адреса, с който сте регистрирани и ще ви изпратим
            потвърждение с линк за промяна на паролата.
          </Text>
          <View style={tw`w-full`}>
            <TextInput
              name="email"
              label="Email"
              placeholder="Вашият email адрес"
              rules={{ required: 'Задължително поле', validate: isValidEmail }}
              control={control}
              returnKeyType="done"
              onSubmitEditing={handleSubmit(submit)}
            />

            <Spacer h={10} />

            <ActionButton
              label="Изпрати →"
              onPress={handleSubmit(submit)}
              loading={isSubmitting}
            />
          </View>

          <Spacer h={5} />

          <View style={tw`w-full items-center`}>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={tw`text-base text-black font-body-semibold`}>
                Вход
              </Text>
            </TouchableOpacity>
          </View>
        </Container>
      </SafeAreaView>
    </KeyboardAwareScrollView>
  );
};

export default ForgottenPassword;
