import { useScrollToTop } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { ActivityIndicator, View, useWindowDimensions } from 'react-native';

import { RecipeGridItem } from '@features/recipes';
import ErrorMessage from '@features/ui/shared/ErrorMessage';
import Text from '@features/ui/shared/Text';
import { getFavourites } from '@utils/queries';
import { useDataStore } from '@utils/store';
import tw from '@utils/tailwind';
import { theme } from 'tailwind.config';

const ITEMS_PER_ROW = 2;
const ITEMS_SPACING = 15;

const Favourites = () => {
  const listRef = React.useRef(null);
  const { width } = useWindowDimensions();
  const favourites = useDataStore(state => state.favourites);

  useScrollToTop(listRef);

  const recipeIds = React.useMemo(
    () => Object.keys(favourites).join(','),
    [favourites],
  );

  const {
    data: favouritesData,
    isSuccess,
    isError,
    error,
    isLoading,
  } = useQuery(['favourites', recipeIds], () => getFavourites(recipeIds), {
    enabled: !!recipeIds,
  });

  const itemSize = React.useMemo(() => {
    const availableWidth = width - ITEMS_SPACING * (ITEMS_PER_ROW + 1);
    return availableWidth / ITEMS_PER_ROW;
  }, [width]);

  return (
    <React.Fragment>
      <View style={tw`bg-white flex-1`}>
        {isError && !isLoading ? <ErrorMessage>{error}</ErrorMessage> : null}

        {isLoading && !!recipeIds ? (
          <View style={tw`flex-1 items-center justify-center`}>
            <ActivityIndicator
              size={'large'}
              color={theme.extend.colors.primary}
            />
          </View>
        ) : null}

        {!recipeIds ? (
          <View style={tw`py-10 px-8 items-center`}>
            <Text style={tw`text-center`}>
              Все още нямате добавени любими рецепти.
            </Text>
          </View>
        ) : null}

        {isSuccess && !isLoading ? (
          <View style={tw`flex-1 mt-[2px]`}>
            <FlashList
              ref={listRef}
              key={`grid-${itemSize}`}
              data={favouritesData}
              numColumns={ITEMS_PER_ROW}
              renderItem={({ item: recipe }) => (
                <View style={tw`flex-1 px-[7.5px] mb-[17px]`}>
                  <RecipeGridItem recipe={recipe} isBookLabelVisible />
                </View>
              )}
              estimatedItemSize={itemSize}
              contentContainerStyle={tw`pt-[7.5px] px-[11px]`}
            />
          </View>
        ) : null}
      </View>
    </React.Fragment>
  );
};

export default React.memo(Favourites);
