import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import axios, { AxiosError } from 'axios';
import React from 'react';
import { useForm } from 'react-hook-form';
import { Alert, View } from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { ActionButton } from '@features/ui/button';
import Container from '../../features/ui/shared/Container';
import DeleteProfile from '../../features/profile/DeleteProfile';
import Spacer from '../../features/ui/shared/Spacer';
import TextInput from '../../features/ui/shared/TextInput';
import { updateUserInfo } from '../../utils/auth-actions';
import tw from '../../utils/tailwind';
import { useMe } from '../../utils/hooks';
import { queryClient } from '../../utils/queries';

interface UpdateProfileForm {
  name: string;
}

const Profile = () => {
  const { data: user } = useMe();

  const {
    control,
    handleSubmit,
    formState: { isSubmitting },
  } = useForm<UpdateProfileForm>();

  const submit = async ({ name }) => {
    try {
      await updateUserInfo({ name });

      Alert.alert('Профила ви бе успешно обновен!');

      queryClient.invalidateQueries(['me']);
    } catch (error) {
      if (!axios.isAxiosError(error)) {
        console.error('Regular error');
        console.error(error);
      }

      // Handle axios errors
      const axiosError = error as AxiosError;

      if (axiosError.response?.status === 422) {
        Alert.alert(axiosError.message);
        return;
      }

      console.error((axiosError.response?.data as any).message);
      Alert.alert((axiosError.response?.data as any).message);
    }
  };

  return (
    <KeyboardAwareScrollView
      enableOnAndroid
      bounces={false}
      extraScrollHeight={20}
      contentContainerStyle={tw`flex-1 bg-white py-10`}>
      {user ? (
        <Container style={tw`w-full items-center`}>
          <View style={tw`w-full`}>
            <TextInput
              name="email"
              label="Email"
              control={control}
              defaultValue={user?.email}
              disabled
            />
            <Spacer h={5} />
            <TextInput
              name="name"
              label="Вашите имена"
              placeholder="Име и фамилия"
              rules={{ required: 'Задължително поле' }}
              control={control}
              defaultValue={user?.name}
            />
            <Spacer h={10} />
            <ActionButton
              label="Запази"
              onPress={handleSubmit(submit)}
              loading={isSubmitting}
            />
          </View>
        </Container>
      ) : (
        <Skeleton />
      )}

      <DeleteProfile />
    </KeyboardAwareScrollView>
  );
};

const Skeleton = () => (
  <SkeletonPlaceholder>
    <View style={tw`w-full`} />
  </SkeletonPlaceholder>
);

export default Profile;
