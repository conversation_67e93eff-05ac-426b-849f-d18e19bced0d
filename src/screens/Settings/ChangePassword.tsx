import { useNavigation } from '@react-navigation/native';
import axios, { AxiosError } from 'axios';
import React from 'react';
import { useForm } from 'react-hook-form';
import { Alert, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

import { ActionButton } from '@features/ui/button';
import Container from '../../features/ui/shared/Container';
import Spacer from '../../features/ui/shared/Spacer';
import TextInput from '../../features/ui/shared/TextInput';
import { changePassword } from '../../utils/auth-actions';
import tw from '../../utils/tailwind';

interface ChangePasswordForm {
  password: string;
  new_password_confirmation: string;
  new_password: string;
}

const ChangePassword = () => {
  const passwordRef = React.useRef({});
  const navigation = useNavigation();

  const {
    control,
    handleSubmit,
    watch,
    reset,
    formState: { isSubmitting },
  } = useForm<ChangePasswordForm>();

  passwordRef.current = watch('new_password', '');

  const submit = async ({
    password,
    new_password,
    new_password_confirmation,
  }) => {
    try {
      await changePassword({
        password,
        new_password,
        new_password_confirmation,
      });

      Alert.alert('Паролата ви бе успешно обновена!');

      reset();

      navigation.navigate('Settings' as never);
    } catch (error) {
      if (!axios.isAxiosError(error)) {
        console.error('Regular error');
        console.error(error);
      }

      // Handle axios errors
      const axiosError = error as AxiosError;

      if (axiosError.response?.status === 422) {
        Alert.alert(
          (axiosError.response as any)?.data?.message || axiosError.message,
        );
        return;
      }

      console.error((axiosError.response?.data as any).message);
      Alert.alert((axiosError.response?.data as any).message);
    }
  };

  return (
    <KeyboardAwareScrollView
      enableOnAndroid
      bounces={false}
      extraScrollHeight={20}
      contentContainerStyle={tw`flex-1 bg-white py-10`}>
      <Container style={tw`w-full items-center`}>
        <View style={tw`w-full`}>
          <TextInput
            name="password"
            label="Текуща парола"
            rules={{ required: 'Задължително поле' }}
            control={control}
            secureTextEntry
          />
          <Spacer h={5} />

          <TextInput
            name="new_password"
            label="Нова парола"
            rules={{ required: 'Задължително поле' }}
            control={control}
            secureTextEntry
          />
          <Spacer h={5} />
          <TextInput
            name="new_password_confirmation"
            label="Потвърждение на новата паролата"
            rules={{
              required: 'Задължително поле',
              validate: value =>
                value === passwordRef.current || 'Паролите не съвпадат',
            }}
            control={control}
            secureTextEntry
          />

          <Spacer h={10} />

          <ActionButton
            label="Смени паролата"
            onPress={handleSubmit(submit)}
            loading={isSubmitting}
          />
        </View>
      </Container>
    </KeyboardAwareScrollView>
  );
};

export default ChangePassword;
