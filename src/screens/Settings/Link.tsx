import React from 'react';
import { useLinkTo } from '@react-navigation/native';
import { Linking, TouchableOpacity, View } from 'react-native';
import {
  ChevronRightIcon,
  ArrowTopRightOnSquareIcon,
} from 'react-native-heroicons/outline';
import Text from '../../features/ui/shared/Text';
import tw from '../../utils/tailwind';

interface LinkProps {
  to?: string;
  external?: boolean;
  withSubscription?: boolean;
  children: any;
  onPress?: () => void;
}

const Link = ({
  to,
  external,
  children,
  withSubscription,
  onPress,
}: LinkProps) => {
  const linkTo = useLinkTo();

  const navigate = () => {
    if (!to) {
      return;
    }

    if (withSubscription) {
      // SheetManager.show('premium-sheet');
      // return;
    }

    if (external) {
      Linking.openURL(to);
      return;
    }

    linkTo(to);
  };

  return (
    <TouchableOpacity style={tw`w-full`} onPress={onPress || navigate}>
      <View
        style={tw`border-b w-full py-4 border-slate-200 flex-row items-center justify-between`}>
        <Text style={tw`text-sm font-body-semibold text-slate-600`}>
          {children}
        </Text>
        {external ? (
          <ArrowTopRightOnSquareIcon style={tw`text-slate-400`} />
        ) : (
          <ChevronRightIcon style={tw`text-slate-400`} />
        )}
      </View>
    </TouchableOpacity>
  );
};

export default Link;
