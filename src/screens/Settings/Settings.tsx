import { useNavigation } from '@react-navigation/native';
import { updateSubscriptionStatusManually } from '@utils/api';
import React from 'react';
import { Alert, Linking, ScrollView, View } from 'react-native';
import Purchases, { PurchasesEntitlementInfo } from 'react-native-purchases';
import MeasurementSwitch from '../../features/profile/MeasurementSwitch';
import Container from '../../features/ui/shared/Container';
import Text from '../../features/ui/shared/Text';
import { ENTITLEMENT_ID } from '../../utils/iap';
import { useDataStore, usePersistedStore } from '../../utils/store';
import tw from '../../utils/tailwind';
import Link from './Link';

const Settings = () => {
  const navigation = useNavigation();
  const token = usePersistedStore(state => state.token);
  const isUserPremium = useDataStore(state => state.isUserPremium);
  const setIsUserPremium = useDataStore(state => state.setIsUserPremium);
  const [activeEntitlement, setActiveEntitlement] =
    React.useState<PurchasesEntitlementInfo>();

  const handleAppNavigation = (screenName: string) => {
    navigation.navigate(screenName as never);
  };

  const openBillingPortal = async () => {
    try {
      const { managementURL } = await Purchases.getCustomerInfo();

      if (!managementURL) {
        throw new Error();
      }

      if (await Linking.canOpenURL(managementURL)) {
        await Linking.openURL(managementURL);
      }
    } catch (error) {
      Alert.alert('Грешка!', 'Възникна проблем при отваряне на настройките');
    }
  };

  const restorePurchases = async () => {
    try {
      const customerInfo = await Purchases.restorePurchases();

      const isSubscribed =
        typeof customerInfo.entitlements.active[ENTITLEMENT_ID] !== 'undefined';

      setIsUserPremium(isSubscribed);

      await updateSubscriptionStatusManually(token, isSubscribed);
    } catch (error: any) {
      console.log(error);
      Alert.alert(error?.message);
    }
  };

  React.useEffect(() => {
    async function getCustomerInfo() {
      const { entitlements } = await Purchases.getCustomerInfo();

      if (typeof entitlements.active[ENTITLEMENT_ID] !== 'undefined') {
        setActiveEntitlement(entitlements.active[ENTITLEMENT_ID]);
      }
    }

    getCustomerInfo();
  }, []);

  return (
    <View style={tw`flex-1`}>
      <ScrollView
        bounces={false}
        contentContainerStyle={tw`bg-white flex-1 pt-2 pb-10`}>
        <Container>
          <Link onPress={() => handleAppNavigation('Profile')}>
            Редакция на профил
          </Link>
          <Link onPress={() => handleAppNavigation('ChangePassword')}>
            Смяна на парола
          </Link>
          <Link external to="https://veganna.bg/privacy-policy/">
            Политика за поверителност
          </Link>
          <MeasurementSwitch />
          {isUserPremium && activeEntitlement?.expirationDate ? (
            <React.Fragment>
              <View style={tw`mt-10 p-4 bg-background rounded-lg`}>
                <Text>
                  Вашия абонамент ще{' '}
                  {activeEntitlement?.willRenew ? 'се поднови' : 'изтече'} на{' '}
                  {new Date(
                    activeEntitlement?.expirationDate,
                  ).toLocaleDateString('bg')}
                </Text>
              </View>
              <Link onPress={openBillingPortal} external>
                Управление на абонамента
              </Link>
            </React.Fragment>
          ) : null}

          {!isUserPremium ? (
            <>
              <Link onPress={restorePurchases}>Възстанови покупка</Link>
            </>
          ) : null}
        </Container>
      </ScrollView>
    </View>
  );
};

export default Settings;
