export interface IngredientItem {
  metric: string;
  imperial: string;
  checked: boolean;
}

export interface RecipeIngredients {
  [key: string]: IngredientItem;
}

/**
 * Helper to merge duplicate ingredients within a recipe.
 */
export const mergeIngredients = (
  items: RecipeIngredients,
  system: 'metric' | 'imperial',
): RecipeIngredients => {
  // group by unit or key
  const groups = Object.entries(items).reduce(
    (acc, [key, item]) => {
      const text = system === 'metric' ? item.metric : item.imperial;
      const match = /^([\d.]+)\s+(.+)$/.exec(text);
      if (match) {
        const qty = parseFloat(match[1]);
        const unit = match[2];
        acc[unit]
          ? (acc[unit].qty += qty)
          : (acc[unit] = {
              qty,
              unit,
              key,
              checked: item.checked,
              metric: item.metric,
              imperial: item.imperial,
            });
      } else {
        acc[key] = {
          qty: NaN,
          unit: key,
          key,
          checked: item.checked,
          metric: item.metric,
          imperial: item.imperial,
        };
      }
      return acc;
    },
    {} as Record<
      string,
      {
        qty: number;
        unit: string;
        key: string;
        checked: boolean;
        metric: string;
        imperial: string;
      }
    >,
  );
  // build merged result
  return Object.values(groups).reduce(
    (res, { key, qty, unit, checked, metric, imperial }) => {
      if (!isNaN(qty)) {
        const display = `${qty} ${unit}`;
        res[key] = {
          metric: system === 'metric' ? display : metric,
          imperial: system === 'imperial' ? display : imperial,
          checked,
        };
      } else {
        res[key] = { metric, imperial, checked };
      }
      return res;
    },
    {} as RecipeIngredients,
  );
};
