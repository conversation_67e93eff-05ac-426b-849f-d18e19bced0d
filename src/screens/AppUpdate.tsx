import React from 'react';
import { Linking, Modal, Platform, Pressable, View } from 'react-native';
import { ArrowUpTrayIcon } from 'react-native-heroicons/outline';

import { ActionButton } from '@features/ui/button';
import Container from '@features/ui/shared/Container';
import Heading from '@features/ui/shared/Heading';
import Text from '@features/ui/shared/Text';
import { useDataStore } from '@utils/store';
import tw from '@utils/tailwind';

const AppUpdate: React.FC = () => {
  const visible = useDataStore(state => state.shouldShowAppUpdateScreen);
  const setVisible = useDataStore(state => state.setShouldShowAppUpdateScreen);
  const isCriticalUpdate = useDataStore(state => state.isCriticalUpdate);
  const updateUrl = useDataStore(state => state.updateUrl);

  const handleUpdatePress = async () => {
    try {
      let storeUrl = '';

      if (updateUrl) {
        storeUrl = updateUrl;
      } else {
        if (Platform.OS === 'ios') {
          storeUrl = `https://apps.apple.com/us/app/veganna/id1661979311?uo=4`;
        } else if (Platform.OS === 'android') {
          storeUrl = `https://play.google.com/store/apps/details?id=bg.veganna.app&hl=us`;
        }
      }

      const canOpen = await Linking.canOpenURL(storeUrl);

      if (canOpen) {
        await Linking.openURL(storeUrl);

        if (!isCriticalUpdate) {
          setVisible(false);
        }
      } else {
        console.error('[UpdateModal] Cannot open store URL:', storeUrl);
      }
    } catch (error) {
      console.error('[UpdateModal] Error opening store:', error);
    }
  };

  const handleClosePress = () => {
    if (!isCriticalUpdate) {
      setVisible(false);
    }
  };

  return (
    <Modal
      animationType="slide"
      visible={visible}
      transparent={false}
      presentationStyle="fullScreen"
      statusBarTranslucent={true}
      onRequestClose={handleClosePress}>
      <View style={tw`absolute inset-0 w-full h-full bg-white`}>
        <Container style={tw`flex-1 justify-center items-center py-8 px-8`}>
          <View style={tw`items-center mb-8 w-full`}>
            <View style={tw`bg-primary p-4 rounded-full mb-5`}>
              <ArrowUpTrayIcon size={48} color="#FFFFFF" />
            </View>

            <Heading style={tw`text-center text-[34px] font-semibold my-4`}>
              {isCriticalUpdate
                ? 'Необходима актуализация'
                : 'Налична актуализация'}
            </Heading>

            <Text style={tw`text-center mb-6 text-gray-600 px-2 text-base`}>
              {isCriticalUpdate
                ? 'Нова версия е налична. Моля, актуализирайте, за да получите достъп до всички функции и подобрения в приложението.'
                : 'Имаме нова версия с подобрения и функции, създадени специално за вас. Актуализирайте сега за по-добро изживяване.'}
            </Text>
          </View>

          <View style={tw`w-full px-2`}>
            <ActionButton
              label="Актуализирай"
              onPress={handleUpdatePress}
              style={tw`mb-4`}
            />

            {!isCriticalUpdate && (
              <Pressable
                onPress={handleClosePress}
                style={({ pressed }) => [
                  tw`py-3 px-6`,
                  pressed ? { opacity: 0.7 } : {},
                ]}>
                <Text style={tw`text-center text-gray-600`}>По-късно</Text>
              </Pressable>
            )}
          </View>
        </Container>
      </View>
    </Modal>
  );
};

export default React.memo(AppUpdate);
