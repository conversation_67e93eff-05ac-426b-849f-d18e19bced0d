import axios, { AxiosError } from 'axios';
import React from 'react';
import { useForm } from 'react-hook-form';
import {
  Alert,
  Linking,
  SafeAreaView,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Purchases from 'react-native-purchases';

import { ActionButton } from '@features/ui/button';
import CheckBox from '../components/Checkbox';
import Container from '../features/ui/shared/Container';
import Heading from '../features/ui/shared/Heading';
import Spacer from '../features/ui/shared/Spacer';
import Text from '../features/ui/shared/Text';
import TextInput from '../features/ui/shared/TextInput';
import { axiosInstance } from '../utils/api';
import { logout, register } from '../utils/auth-actions';
import { usePersistedStore } from '../utils/store';
import tw from '../utils/tailwind';
import { isValidEmail } from '../utils/validation';
import { OneSignal } from 'react-native-onesignal';
import analytics from '@react-native-firebase/analytics';

interface RegisterForm {
  email: string;
  password: string;
  password_confirmation: string;
  name: string;
}

const Register = () => {
  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { isSubmitting },
  } = useForm<RegisterForm>();

  const [agreedWithGdpr, setAgreedWithGdpr] = React.useState(false);
  const [gdprError, setGdprError] = React.useState('');

  const passwordRef = React.useRef({});
  passwordRef.current = watch('password', '');

  const setPersistedStore = usePersistedStore(state => state.setPersistedStore);

  const submit = async ({ name, email, password, password_confirmation }) => {
    try {
      setGdprError('');

      if (!agreedWithGdpr) {
        setGdprError('Моля съгласете се с политиката за поверителност.');
        return;
      }

      const device_name = DeviceInfo.getDeviceNameSync();

      const token = await register({
        name,
        email,
        password,
        password_confirmation,
        device_name,
      });

      const userData = await axiosInstance.get('/me', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!userData?.data?.uuid) {
        await logout();
        return;
      }

      await Purchases.logIn(userData.data.uuid);
      OneSignal.login(userData.data.uuid);
      OneSignal.User.addEmail(email);
      OneSignal.User.addTag('name', userData.data.name);
      await Purchases.setEmail(email);
      await Purchases.setDisplayName(userData.data.name);
      const oneSignalId = await OneSignal.User.getOnesignalId();
      await Purchases.setOnesignalID(oneSignalId);

      await analytics().setUserId(userData.data.uuid);
      await analytics().setUserProperty('name', userData.data.name);
      await analytics().logEvent('register', {
        id: userData.data.uuid,
      });

      setPersistedStore('token', token);

      reset();
    } catch (err) {
      if (!axios.isAxiosError(err)) {
        console.error('Regular error');
        console.error(err);
      }

      // Handle axios errors
      const axiosError = err as AxiosError;

      if (axiosError.response?.status === 422) {
        Alert.alert(axiosError.message);
        return;
      }

      console.error((axiosError.response?.data as any).message);
      Alert.alert((axiosError.response?.data as any).message);
    }
  };

  return (
    <KeyboardAwareScrollView
      bounces={false}
      enableOnAndroid
      extraScrollHeight={20}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={tw`bg-background min-h-full pb-20`}>
      <SafeAreaView>
        <Container>
          <Heading style={tw`mt-4 text-4xl`}>Създаване на профил</Heading>
          <Text style={tw`mt-2 mb-6 text-sm max-w-[80%] text-black`}>
            Попълнете полетата, за да регистрирате акаунт.
          </Text>
          <TextInput
            name="name"
            label="Вашите имена"
            placeholder="Име и фамилия"
            rules={{ required: 'Задължително поле' }}
            control={control}
          />
          <Spacer h={5} />
          <TextInput
            name="email"
            label="Email"
            placeholder="Въведете вашия email"
            rules={{
              required: 'Задължително поле',
              validate: isValidEmail,
            }}
            control={control}
          />
          <Spacer h={5} />
          <TextInput
            name="password"
            label="Парола"
            placeholder="Минимум 6 символа"
            rules={{ required: 'Задължително поле' }}
            control={control}
            secureTextEntry
          />
          <Spacer h={5} />
          <TextInput
            name="password_confirmation"
            label="Потвърждение на паролата"
            placeholder="Минимум 6 символа"
            rules={{
              required: 'Задължително поле',
              validate: value =>
                value === passwordRef.current || 'Паролите не съвпадат',
            }}
            control={control}
            secureTextEntry
          />
          <Spacer h={5} />

          <View style={tw`flex-row items-center overflow-hidden`}>
            <TouchableOpacity
              style={tw`px-2`}
              onPress={() => setAgreedWithGdpr(state => !state)}>
              <CheckBox selected={agreedWithGdpr} />
            </TouchableOpacity>
            <Text style={tw`pl-3 text-black text-sm flex-shrink`}>
              Съгласявам се с{' '}
              <TouchableWithoutFeedback
                onPress={() =>
                  Linking.openURL('https://veganna.bg/privacy-policy/')
                }>
                <Text style={tw`text-black text-sm underline`}>
                  политиката за поверителност на данните на Veganna
                </Text>
              </TouchableWithoutFeedback>
            </Text>
          </View>
          {!!gdprError && (
            <View>
              <Text
                style={tw`text-red-500 leading-tight font-body-semibold mt-2 text-[12px]`}>
                {gdprError}
              </Text>
            </View>
          )}

          <Spacer h={10} />

          <ActionButton
            label="Регистрация"
            onPress={handleSubmit(submit)}
            loading={isSubmitting}
          />
        </Container>
      </SafeAreaView>
    </KeyboardAwareScrollView>
  );
};

export default Register;
