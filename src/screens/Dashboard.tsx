import analytics from '@react-native-firebase/analytics';
import { useScrollToTop } from '@react-navigation/native';
import React from 'react';
import { SafeAreaView, ScrollView } from 'react-native';
import { OneSignal } from 'react-native-onesignal';
import Purchases from 'react-native-purchases';

import AppDashboardHeader from '../components/AppDashboardHeader';
import BookBanners from '../components/BookBanners/index';
import DynamicHomeContent from '../components/DynamicHomeContent';
import GetPremium from '../components/GetPremium';
import * as Queries from '../utils/queries';
import { useDataStore } from '../utils/store';
import tw from '../utils/tailwind';

const Dashboard = () => {
  const scrollViewRef = React.useRef(null);
  const isUserPremium = useDataStore(state => state.isUserPremium);

  useScrollToTop(scrollViewRef);

  React.useEffect(() => {
    (async () => {
      const personalData = await Queries.me();
      OneSignal.login(personalData.uuid);
      OneSignal.User.addEmail(personalData.email);
      OneSignal.User.addTag('name', personalData.name);
      const oneSignalId = await OneSignal.User.getOnesignalId();
      await Purchases.logIn(personalData.uuid);
      await Purchases.setEmail(personalData.email);
      await Purchases.setDisplayName(personalData.name);
      await Purchases.setOnesignalID(oneSignalId);
      await analytics().setUserId(personalData.uuid);
      await analytics().setUserProperty('name', personalData.name);
      await analytics().logEvent('dashboard_opened');
      OneSignal.Notifications.requestPermission(false);

      // Method for listening for notification clicks
      OneSignal.Notifications.addEventListener('click', event => {
        console.log('OneSignal: notification clicked:', event);
      });
    })();

    return () => {};
  }, []);

  return (
    <React.Fragment>
      <ScrollView
        ref={scrollViewRef}
        bounces={false}
        contentContainerStyle={tw`bg-white justify-start flex-grow`}
        showsVerticalScrollIndicator={false}>
        <SafeAreaView style={tw`bg-white flex-1`}>
          <AppDashboardHeader style={tw`pt-4`} />

          <BookBanners />

          <DynamicHomeContent />

          {!isUserPremium ? <GetPremium /> : null}
        </SafeAreaView>
      </ScrollView>
    </React.Fragment>
  );
};

export default Dashboard;
