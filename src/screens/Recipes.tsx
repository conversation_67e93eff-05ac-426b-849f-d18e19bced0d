import React, { useCallback, useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { CalendarDaysIcon } from 'react-native-heroicons/outline';

import {
  CallToActionBar,
  FiltersModal,
  RecipeGridHeader,
  RecipeGridList,
} from '@features/recipes';
import tw from '@utils/tailwind';
import { theme } from '../../tailwind.config';

/**
 * TEMP: The current filtering is a temporary solution until the api is configured.
 */
const Recipes = () => {
  const navigation = useNavigation<any>();
  const [showFreeRecipesOnly, setShowFreeRecipesOnly] = useState(false);

  const handleShowAllRecipes = useCallback(() => {
    setShowFreeRecipesOnly(false);
  }, []);

  const handleShowFreeRecipes = useCallback(() => {
    setShowFreeRecipesOnly(true);
  }, []);

  const navigateToMealPlanner = useCallback(() => {
    navigation.navigate('MealPlanner');
  }, [navigation]);

  return (
    <View style={tw`bg-white flex-1`}>
      <View style={tw`flex-row items-center justify-between`}>
        <View style={tw`flex-1`}>
          <RecipeGridHeader />
        </View>
        <TouchableOpacity
          onPress={navigateToMealPlanner}
          style={tw`pr-4 pl-2 py-2`}
        >
          <CalendarDaysIcon size={24} color={theme.extend.colors.primary} />
        </TouchableOpacity>
      </View>
      <RecipeGridList showFreeRecipesOnly={showFreeRecipesOnly} />
      <FiltersModal />
      <CallToActionBar
        showFreeRecipesOnly={showFreeRecipesOnly}
        onShowAllRecipes={handleShowAllRecipes}
        onShowFreeRecipes={handleShowFreeRecipes}
      />
    </View>
  );
};

export default React.memo(Recipes);
