import { FlashList } from '@shopify/flash-list';
import React from 'react';
import { ActivityIndicator, View, useWindowDimensions } from 'react-native';

import {
  RecipeGridFooter,
  RecipeGridItem,
  Spinner,
  transformInfinitelyPaginatedRecipes,
  useInfiniteBookRecipes,
} from '@features/recipes';
import ErrorMessage from '@features/ui/shared/ErrorMessage';
import tw from '@utils/tailwind';

type Props = {
  bookId: number;
};

const ITEMS_PER_ROW = 2;
const ITEMS_SPACING = 15;

const BookRecipes = ({ bookId }: Props) => {
  const { width } = useWindowDimensions();
  const query = useInfiniteBookRecipes(bookId);

  const loadMore = () => {
    if (!query.hasNextPage) return;
    query.fetchNextPage();
  };

  const renderGridFooter = () =>
    query.hasNextPage ? (
      query.isFetchingNextPage ? (
        <Spinner />
      ) : null
    ) : (
      <RecipeGridFooter />
    );

  const itemSize = React.useMemo(() => {
    const availableWidth = width - ITEMS_SPACING * (ITEMS_PER_ROW + 1);
    return availableWidth / ITEMS_PER_ROW;
  }, [width]);

  if (query.isError) {
    return <ErrorMessage />;
  }

  if (query.data) {
    const recipes = transformInfinitelyPaginatedRecipes(query.data.pages);

    return (
      <View style={tw`flex-1`}>
        <FlashList
          key={`grid-${itemSize}`}
          data={recipes}
          numColumns={ITEMS_PER_ROW}
          renderItem={({ item: recipe, index }) => (
            <View
              style={tw`flex-1 mb-[15px] ${
                index % 2 === 0 ? 'mr-[15px]' : ''
              }`}>
              <RecipeGridItem recipe={recipe} isBookLabelVisible={false} />
            </View>
          )}
          estimatedItemSize={itemSize}
          contentContainerStyle={tw`pt-[7.5px]`}
          onEndReached={loadMore}
          onEndReachedThreshold={0.3}
          ListFooterComponent={renderGridFooter}
        />
      </View>
    );
  }

  return <ActivityIndicator size="large" />;
};

export default React.memo(BookRecipes);
