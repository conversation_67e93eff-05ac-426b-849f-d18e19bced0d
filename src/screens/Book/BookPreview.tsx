import React from 'react';
import { View } from 'react-native';

import RemoteImage from '@components/RemoteImage';
import { Book } from '@utils/queries';
import Container from '../../features/ui/shared/Container';
import Heading from '../../features/ui/shared/Heading';
import HtmlRenderer from '../../features/ui/shared/HtmlRenderer';
import tw from '../../utils/tailwind';

const BookPreview = ({ title, description, preview }: Book) => {
  return (
    <View>
      {preview ? (
        <RemoteImage
          uri={preview}
          containerStyle={tw`h-[450px] w-full`}
        />
      ) : null}

      <Container style={tw`my-6`}>
        <Heading>{title}</Heading>
        <HtmlRenderer html={description} />
      </Container>
    </View>
  );
};

export default BookPreview;
