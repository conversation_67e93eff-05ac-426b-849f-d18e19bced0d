import React from 'react';
import { ActionButton } from '@features/ui/button';
import Container from '../../features/ui/shared/Container';
import { useBook } from '../../utils/hooks';
import GenericLoadingScreen from '../../features/ui/shared/GenericLoadingScreen';
import BookPreview from './BookPreview';
import BookRecipes from './BookRecipes';
import { useQuestionnaireActions } from '@features/questionnaires';
import { useFocusEffect } from '@react-navigation/native';
import Heading from '@features/ui/shared/Heading';
import VList from '@features/ui/shared/VList';
import tw from '@utils/tailwind';

const Book = ({ route, navigation }) => {
  const bookId = route.params.bookId;

  const { initQuestionnaire } = useQuestionnaireActions();

  useFocusEffect(initQuestionnaire);

  const { book, isLoading, isUserOwnedBook } = useBook(bookId);

  if (isLoading) return <GenericLoadingScreen />;

  if (!book) return null;

  return (
    <VList>
      <BookPreview {...book} />

      <Container style={tw`px-4`}>
        {isUserOwnedBook ? (
          <>
            <Heading>Рецепти</Heading>
            <BookRecipes bookId={bookId} />
          </>
        ) : (
          <ActionButton
            label="Отключи рецептите от книгата"
            onPress={() =>
              navigation.navigate('Questionnaire', {
                bookId,
                timeout: book.questionnaire_timeout,
              })
            }
          />
        )}
      </Container>
    </VList>
  );
};

export default Book;
