import React, { useEffect } from 'react';
import { ScrollView, View } from 'react-native';
import Container from '../features/ui/shared/Container';
import tw from '../utils/tailwind';
import KeyboardShift from '../components/KeyboardShift';
import {
  QuestionnaireCountdown,
  QuestionnaireForm,
  QuestionnaireInstructions,
  useQuestionnaireStore,
} from '@features/questionnaires';

const Questionnaire = ({ route, navigation }) => {
  const { bookId, timeout } = route.params;
  const isRunning = useQuestionnaireStore(store => store.isRunning);
  const isSuccessful = useQuestionnaireStore(store => store.isSuccessful);

  useEffect(() => {
    if (!isSuccessful) return;

    navigation.goBack();
  }, [isSuccessful]);

  return (
    <KeyboardShift>
      <View style={tw`flex-1 bg-stone-50`}>
        <ScrollView
          style={tw`flex-1`}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={tw`py-10 relative`}>
          <Container>
            {isRunning ? (
              <QuestionnaireForm bookId={bookId} />
            ) : (
              <QuestionnaireInstructions timeout={timeout} />
            )}
          </Container>
        </ScrollView>
        {isRunning ? <QuestionnaireCountdown timeout={timeout} /> : null}
      </View>
    </KeyboardShift>
  );
};

export default Questionnaire;
