import { View, ActivityIndicator } from 'react-native';
import React from 'react';
import tw from '../utils/tailwind';
import Logo from '../assets/svg/Logo';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

const UpdatingApp = () => {
  return (
    <Animated.View
      style={tw`bg-background flex-1 items-center justify-center`}
      entering={FadeIn}
      exiting={FadeOut}>
      <View style={tw`gap-y-10`}>
        <Logo style={tw`mx-auto`} />
        <ActivityIndicator size="large" color="#D6837B" />
      </View>
    </Animated.View>
  );
};

export default UpdatingApp;
