import { useHeaderHeight } from '@react-navigation/elements';
import { useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { Linking, ScrollView, TouchableOpacity, View } from 'react-native';
import {
  EllipsisHorizontalIcon,
  PlayCircleIcon,
} from 'react-native-heroicons/outline';

import RemoteImage from '@components/RemoteImage';
import { theme } from '../../tailwind.config';
import ChallengeItemRow from '../features/challenges/ChallengeItemRow';
import PortionsModal from '../features/challenges/PortionsModal';
import Container from '../features/ui/shared/Container';
import ErrorMessage from '../features/ui/shared/ErrorMessage';
import Heading from '../features/ui/shared/Heading';
import HtmlRenderer from '../features/ui/shared/HtmlRenderer';
import Text from '../features/ui/shared/Text';
import { getChallengeById } from '../utils/queries';
import { useDataStore } from '../utils/store';
import tw from '../utils/tailwind';
import { ChallengeItem } from './Challenges';

const Challenge = ({ route }) => {
  const [modalVisible, setModalVisible] = React.useState(false);
  const challengeId = React.useMemo(
    () => route?.params?.challengeId,
    [route?.params?.challengeId],
  );

  const {
    data: challenge,
    isLoading,
    isError,
    isSuccess,
  } = useQuery(
    ['challenge', challengeId],
    () => getChallengeById(challengeId),
    {
      enabled: challengeId !== undefined,
    },
  );

  const showPremiumModal = useDataStore(
    state => state.setShouldShowPremiumModal,
  );

  const challengePortions = useDataStore(
    state => state.challengePortions[challengeId],
  );

  const isUserPremium = useDataStore(state => state.isUserPremium);

  const navigation = useNavigation<any>();
  const headerHeight = useHeaderHeight();

  const navigateToChallengeItem = React.useCallback(
    (item: ChallengeItem) => {
      navigation.navigate('ChallengeItem', {
        ...item,
        challengeId: challenge!.id,
      });
    },
    [navigation, challenge],
  );

  if (isError) {
    return <ErrorMessage />;
  }
  return (
    <React.Fragment>
      {isSuccess && !isLoading ? (
        <React.Fragment>
          <ScrollView
            overScrollMode="never"
            style={[tw`bg-white`, { marginTop: -headerHeight }]}
            contentInsetAdjustmentBehavior="always"
            contentContainerStyle={[
              tw`bg-white`,
              tw.style(!challenge?.image && 'pt-20'),
            ]}
            showsVerticalScrollIndicator={false}
            bounces={false}>
            {challenge?.image ? (
              <RemoteImage
                uri={challenge.image}
                containerStyle={tw`h-[400px] w-auto`}
              />
            ) : null}

            <View style={tw`bg-white -mt-10 rounded-t-xl pt-10`}>
              <Container>
                <View style={tw`flex-row justify-between items-start`}>
                  <Heading style={tw`text-4xl flex-shrink pr-3`}>
                    {challenge.name}
                  </Heading>
                  <TouchableOpacity onPress={() => setModalVisible(true)}>
                    <EllipsisHorizontalIcon
                      color={theme.extend.colors.black}
                      size={30}
                    />
                  </TouchableOpacity>
                </View>
                <Text style={tw`mt-1 text-black`}>
                  {challenge.duration}, порции за {challengePortions}{' '}
                  {challengePortions > 1 ? 'човека' : 'човек'}
                </Text>
              </Container>
              <View style={[tw`my-1 items-center py-2`]}>
                {!isUserPremium ? (
                  <TouchableOpacity
                    onPress={() => showPremiumModal(true)}
                    activeOpacity={0.9}
                    style={tw`flex-1 bg-primary mt-4 mb-2 px-10 py-4 rounded-lg mx-6 self-stretch items-center justify-center`}>
                    <Text style={tw`font-body-bold text-white text-center`}>
                      Започни сега
                    </Text>
                  </TouchableOpacity>
                ) : null}

                {challenge?.external_link ? (
                  <TouchableOpacity
                    style={tw`bg-background w-full px-6 my-4 flex-row items-center justify-between py-3`}
                    onPress={() =>
                      challenge?.external_link &&
                      Linking.openURL(challenge?.external_link)
                    }>
                    <Text style={tw`text-sm flex-shrink`}>
                      Гледайте видео към режима
                    </Text>
                    <PlayCircleIcon
                      size={30}
                      color={theme.extend.colors.black}
                    />
                  </TouchableOpacity>
                ) : null}
              </View>
              <Container>
                <Heading>За хранителния режим</Heading>
                <HtmlRenderer html={challenge.about} />
              </Container>
              <Container>
                <View style={tw`flex-row items-center justify-between my-4`}>
                  <Heading>Калории за деня</Heading>
                  <Text style={tw`font-body-bold`}>
                    {challenge.calories_per_day}
                  </Text>
                </View>
              </Container>
              <View style={tw`my-2`} />

              {challenge?.items && challenge?.items?.length > 0
                ? challenge?.items?.map(item => (
                    <ChallengeItemRow
                      key={item.id}
                      name={item.name}
                      onPress={() => navigateToChallengeItem(item)}
                    />
                  ))
                : null}
            </View>
          </ScrollView>
          <PortionsModal
            visible={modalVisible}
            setVisible={setModalVisible}
            mealPlanTitle={challenge.name}
            challengeId={challenge.id}
          />
        </React.Fragment>
      ) : null}
    </React.Fragment>
  );
};

export default Challenge;
