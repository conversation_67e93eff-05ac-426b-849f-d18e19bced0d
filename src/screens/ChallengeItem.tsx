import React from 'react';
import { ScrollView } from 'react-native';
import ChallengeMealItem from '../features/challenges/ChallengeMealItem';
import Container from '../features/ui/shared/Container';
import { useDataStore } from '../utils/store';
import tw from '../utils/tailwind';
import { ChallengeItemMeal } from './Challenges';

const ChallengeItem = ({ route, navigation }) => {
  const { name, meals, challengeId } = React.useMemo(
    () => route?.params,
    [route?.params],
  );

  const challengePortions = useDataStore(
    state => state.challengePortions[challengeId],
  );

  const isUserPremium = useDataStore(state => state.isUserPremium);

  const setShouldShowPremiumModal = useDataStore(
    state => state.setShouldShowPremiumModal,
  );

  async function goToRecipe(meal: ChallengeItemMeal) {
    const isRecipePremium = !!meal.recipe.premium;

    if (isRecipePremium && !isUserPremium) {
      setShouldShowPremiumModal(true);
      return;
    }

    navigation.navigate('Recipe', {
      id: meal.recipe.id,
      title: meal.recipe.title,
      premium: meal.recipe.premium,
      challengePortions: challengePortions,
    });
  }

  React.useEffect(() => {
    navigation.setOptions({
      title: name,
    });
  }, [name, navigation]);

  return (
    <ScrollView
      style={tw`bg-white`}
      contentInsetAdjustmentBehavior="always"
      contentContainerStyle={tw`bg-white py-6`}
      showsVerticalScrollIndicator={false}
      alwaysBounceVertical={true}>
      <Container>
        {meals?.length > 0
          ? meals?.map(meal => (
              <ChallengeMealItem
                key={meal.id}
                meal={meal}
                onPress={goToRecipe}
              />
            ))
          : null}
      </Container>
    </ScrollView>
  );
};

export default ChallengeItem;
