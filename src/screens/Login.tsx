import axios, { AxiosError } from 'axios';
import React from 'react';
import { useForm } from 'react-hook-form';
import { Alert, SafeAreaView, TouchableOpacity, View } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { ActionButton } from '@features/ui/button';
import Text from '../features/ui/shared/Text';
import TextInput from '../features/ui/shared/TextInput';
import { login, logout } from '../utils/auth-actions';
import tw from '../utils/tailwind';

import Purchases from 'react-native-purchases';
import Logo from '../assets/svg/Logo';
import Container from '../features/ui/shared/Container';
import Heading from '../features/ui/shared/Heading';
import Spacer from '../features/ui/shared/Spacer';
import { axiosInstance } from '../utils/api';
import { usePersistedStore } from '../utils/store';
import { isValidEmail } from '../utils/validation';
import { OneSignal } from 'react-native-onesignal';
import analytics from '@react-native-firebase/analytics';

interface LoginForm {
  email: string;
  password: string;
}

const Login = ({ navigation }) => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { isSubmitting },
  } = useForm<LoginForm>();
  const setPersistedStore = usePersistedStore(state => state.setPersistedStore);

  const submit = async ({ email, password }) => {
    try {
      const device_name = DeviceInfo.getDeviceNameSync();

      const token = await login({ email, password, device_name });

      const userData = await axiosInstance.get('/me', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!userData?.data?.uuid) {
        await logout();
        return;
      }

      await analytics().setUserId(userData.data.uuid);
      await analytics().setUserProperty('name', userData.data.name);
      await analytics().logEvent('login', {
        id: userData.data.uuid,
      });
      await Purchases.logIn(userData.data.uuid);
      OneSignal.login(userData.data.uuid);
      OneSignal.User.addEmail(email);
      OneSignal.User.addTag('name', userData.data.name);
      await Purchases.setEmail(email);
      await Purchases.setDisplayName(userData.data.name);
      const oneSignalId = await OneSignal.User.getOnesignalId();
      await Purchases.setOnesignalID(oneSignalId);

      setPersistedStore('token', token);

      reset();
    } catch (err) {
      if (!axios.isAxiosError(err)) {
        console.error(err);
      }

      // Handle axios errors
      const axiosError = err as AxiosError;

      if (axiosError.response?.status === 422) {
        Alert.alert('Грешен имейл или парола');
        return;
      }

      console.error(axiosError);
    }
  };

  return (
    <KeyboardAwareScrollView
      bounces={false}
      enableOnAndroid
      extraScrollHeight={20}
      contentContainerStyle={tw`bg-background min-h-full pb-20`}>
      <Container>
        <SafeAreaView>
          <Logo style={tw`mx-auto my-8`} />
          <Heading style={tw`mx-auto my-4 text-4xl text-secondary`}>
            Вход
          </Heading>
          {/* <UnicornBabyLogo name="login_logo" style={tw`w-60 h-18 mb-10`} /> */}
          <View style={tw`w-full`}>
            <TextInput
              name="email"
              label="Email"
              placeholder="Въведете email"
              rules={{ required: 'Задължително поле', validate: isValidEmail }}
              control={control}
            />
            <Spacer h={5} />
            <TextInput
              name="password"
              label="Парола"
              placeholder="Въведете парола"
              rules={{ required: 'Задължително поле' }}
              control={control}
              secureTextEntry
              returnKeyType="done"
              onSubmitEditing={handleSubmit(submit)}
            />

            <Spacer h={3} />
            <View style={tw`items-end`}>
              <TouchableOpacity
                onPress={() => navigation.push('ForgottenPassword')}>
                <Text style={tw`text-black text-sm font-body-semibold`}>
                  Забравена парола?
                </Text>
              </TouchableOpacity>
            </View>

            <Spacer h={10} />

            <ActionButton
              label="Вход →"
              onPress={handleSubmit(submit)}
              loading={isSubmitting}
            />
          </View>

          <Spacer h={5} />

          <View style={tw`w-full items-center`}>
            <TouchableOpacity onPress={() => navigation.push('Register')}>
              <Text style={tw`text-base text-black font-body-bold`}>
                Регистрация
              </Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Container>
    </KeyboardAwareScrollView>
  );
};

export default Login;
