import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View, ScrollView, SafeAreaView } from 'react-native';
import tw from '@utils/tailwind';

import { WeekSelector } from '@features/meal-planner/components/WeekSelector';
import { DailyMealPlan } from '@features/meal-planner/components/DailyMealPlan';
import { 
  getCurrentWeekStartDate, 
  getWeekDates,
  generateWeekStartDates
} from '@features/meal-planner/utils/date-utils';
import { useMealPlannerStore, useMealPlannerActions } from '@features/meal-planner/meal-planner.store';

const MealPlanner = () => {
  // Get the current week's start date
  const [currentWeekStartDate, setCurrentWeekStartDate] = useState(getCurrentWeekStartDate());
  
  // Generate available weeks (current week + 4 weeks in the future)
  const availableWeeks = useMemo(() => 
    generateWeekStartDates(new Date(), 5, 'future'),
  []);
  
  // Get the days for the current week
  const weekDays = useMemo(() => 
    getWeekDates(currentWeekStartDate),
  [currentWeekStartDate]);
  
  // Get the meal plan for the current week
  const mealPlans = useMealPlannerStore(state => state.mealPlans);
  const { createMealPlan } = useMealPlannerActions();
  
  // Find the current meal plan or create a new one if it doesn't exist
  const currentMealPlan = useMemo(() => 
    mealPlans.find(plan => plan.id === currentWeekStartDate),
  [mealPlans, currentWeekStartDate]);
  
  // Create a meal plan for the current week if it doesn't exist
  useEffect(() => {
    if (!currentMealPlan) {
      createMealPlan(currentWeekStartDate);
    }
  }, [currentMealPlan, currentWeekStartDate, createMealPlan]);
  
  // Handle week change
  const handleWeekChange = useCallback((weekStartDate: string) => {
    setCurrentWeekStartDate(weekStartDate);
  }, []);
  
  return (
    <SafeAreaView style={tw`bg-white flex-1`}>
      <WeekSelector 
        availableWeeks={availableWeeks}
        currentWeekStartDate={currentWeekStartDate}
        onWeekChange={handleWeekChange}
      />
      
      <ScrollView 
        style={tw`flex-1`}
        contentContainerStyle={tw`pb-20`}
        showsVerticalScrollIndicator={false}
      >
        {weekDays.map(day => (
          <DailyMealPlan 
            key={day}
            day={day}
            weekStartDate={currentWeekStartDate}
            mealPlan={currentMealPlan}
          />
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

export default React.memo(MealPlanner);
