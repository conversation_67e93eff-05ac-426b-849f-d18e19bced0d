import { useScrollToTop } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { ActivityIndicator, View } from 'react-native';

import { theme } from 'tailwind.config';
import ShopListItem from '../features/e-shop/ShopListItem';
import ErrorMessage from '../features/ui/shared/ErrorMessage';
import { getProducts, Product } from '../utils/queries';
import tw from '../utils/tailwind';

const Shop = () => {
  const listRef = React.useRef(null);

  useScrollToTop(listRef);
  const { data, isLoading, isError } = useQuery(['products'], getProducts);

  const renderItem = React.useCallback(
    ({ item: product }: { item: Product }) => <ShopListItem {...product} />,
    [],
  );

  return (
    <React.Fragment>
      <View style={tw`bg-white flex-1`}>
        {isLoading ? (
          <View style={tw`flex-1 items-center justify-center`}>
            <ActivityIndicator
              size={'large'}
              color={theme.extend.colors.primary}
            />
          </View>
        ) : isError ? (
          <ErrorMessage />
        ) : (
          <FlashList
            ref={listRef}
            data={data}
            estimatedItemSize={30}
            keyExtractor={product => product?.id.toString()}
            renderItem={renderItem}
          />
        )}
      </View>
    </React.Fragment>
  );
};

export default React.memo(Shop);
