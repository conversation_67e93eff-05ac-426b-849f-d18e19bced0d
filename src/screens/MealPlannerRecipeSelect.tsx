import React, { useCallback } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ChevronLeftIcon } from 'react-native-heroicons/outline';
import tw from '@utils/tailwind';

import {
  RecipeGridHeader,
  RecipeGridList,
  FiltersModal,
  Recipe,
} from '@features/recipes';
import { MealType, useMealPlannerActions } from '@features/meal-planner/meal-planner.store';
import Text from '@features/ui/shared/Text';
import { theme } from '../../tailwind.config';

interface MealPlannerRecipeSelectRouteParams {
  mealType: MealType;
  day: string;
  weekStartDate: string;
}

// Translate meal types to Bulgarian
const mealTypeLabels: Record<MealType, string> = {
  breakfast: 'Закуска',
  lunch: 'Обяд',
  dinner: 'Вечеря',
};

const MealPlannerRecipeSelect = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const { mealType, day, weekStartDate } = route.params as MealPlannerRecipeSelectRouteParams;
  
  const { addRecipeToMealPlan } = useMealPlannerActions();
  
  // Handle recipe selection
  const handleSelectRecipe = useCallback((recipe: Recipe) => {
    addRecipeToMealPlan(weekStartDate, day, mealType, {
      recipeId: recipe.id,
      title: recipe.title,
      image: recipe.image,
      premium: recipe.premium,
    });
    
    navigation.goBack();
  }, [addRecipeToMealPlan, weekStartDate, day, mealType, navigation]);
  
  return (
    <View style={tw`bg-white flex-1`}>
      {/* Custom header */}
      <View style={tw`flex-row items-center px-4 py-3 border-b border-gray-100`}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <ChevronLeftIcon size={24} color={theme.extend.colors.black} />
        </TouchableOpacity>
        <Text style={tw`flex-1 text-center text-lg font-body-bold text-black`}>
          Избери рецепта за {mealTypeLabels[mealType].toLowerCase()}
        </Text>
        <View style={tw`w-6`} />
      </View>
      
      {/* Recipe grid with selection functionality */}
      <RecipeGridHeader />
      <RecipeGridList 
        showFreeRecipesOnly={false}
        onRecipeSelect={handleSelectRecipe}
      />
      <FiltersModal />
    </View>
  );
};

export default React.memo(MealPlannerRecipeSelect);
