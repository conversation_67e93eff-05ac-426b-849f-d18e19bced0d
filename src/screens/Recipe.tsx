import React from 'react';
import {
  ActivityIndicator,
  Linking,
  RefreshControl,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  EllipsisHorizontalIcon,
  PlayCircleIcon,
} from 'react-native-heroicons/outline';

import { theme } from '../../tailwind.config';
import Container from '../features/ui/shared/Container';
import ErrorMessage from '../features/ui/shared/ErrorMessage';
import Heading from '../features/ui/shared/Heading';
import HtmlRenderer from '../features/ui/shared/HtmlRenderer';
import Text from '../features/ui/shared/Text';
import tw from '../utils/tailwind';

import {
  RecipeGallery,
  RecipeIngredientsList,
  RecipeNutritionInfo,
  RecipePortionControls,
  useRecipeView,
  useSingleRecipe,
} from '@features/recipes';

import RecipeMoreSheet from '@components/RecipeMoreSheet';

const Recipe = ({ route, navigation }) => {
  const isShownInChallenge = !!route?.params?.challengePortions;

  const query = useSingleRecipe();
  useRecipeView(query.data?.id);

  const [portionCount, setPortionCount] = React.useState<number>(
    isShownInChallenge ? route?.params?.challengePortions : 1,
  );
  const [isShareModalVisible, setIsShareModalVisible] = React.useState(false);

  const portionsLabel =
    query?.data?.portions * portionCount > 1 ? 'порции' : 'порция';
  const images = query?.data?.image ? Object.values(query.data.image) : null;

  React.useEffect(() => {
    if (query.data) {
      navigation.setOptions({
        title: query.data.title,
        headerRight: () => (
          <TouchableOpacity
            onPress={() => setIsShareModalVisible(true)}
            style={tw`p-0`}>
            <EllipsisHorizontalIcon
              size={32}
              color={theme.extend.colors.primary}
            />
          </TouchableOpacity>
        ),
      });
    }
  }, [navigation, query.data]);

  if (query.isLoading) {
    return (
      <View style={tw`bg-white flex-1 items-center justify-center`}>
        <ActivityIndicator size={'large'} color={theme.extend.colors.primary} />
      </View>
    );
  }

  if (query.isError) {
    return <ErrorMessage />;
  }

  if (query.data) {
    return (
      <>
        <ScrollView
          style={tw`bg-white`}
          contentInsetAdjustmentBehavior="always"
          contentContainerStyle={tw`bg-white`}
          showsVerticalScrollIndicator={false}
          alwaysBounceVertical={true}
          refreshControl={
            <RefreshControl
              refreshing={query.isRefetching}
              onRefresh={query.refetch}
            />
          }>
          <RecipeGallery images={images} />

          <View style={tw`bg-background/50 py-6`}>
            <Container>
              <View style={tw`flex-row justify-between`}>
                <View style={tw`items-start flex-shrink`}>
                  <Heading>Съставки:</Heading>
                  <Text style={[tw`text-black/70 flex-shrink`]}>
                    за {query.data.portions * portionCount} {portionsLabel}
                  </Text>
                </View>
                {isShownInChallenge ? null : (
                  <RecipePortionControls
                    count={portionCount}
                    setCount={setPortionCount}
                  />
                )}
              </View>
              <RecipeIngredientsList
                recipeTitle={query.data?.title}
                recipePortionSize={query.data?.portions || 1}
                ingredients={query.data?.ingredients}
                portionCount={portionCount}
              />
            </Container>
          </View>
          <View style={tw`bg-white py-6`}>
            <Container>
              {/* Cooking time */}
              <View style={tw`flex-row items-center justify-between`}>
                <Heading>Време за готвене</Heading>
                <Text style={tw`flex-shrink ml-2`}>
                  {query.data?.cooking_time}
                </Text>
              </View>
              {/* Dish information */}
              <View style={tw`mt-6`}>
                <Heading>Информация за ястието</Heading>
                <HtmlRenderer html={query.data?.description} />
              </View>
              {/* Instructions */}
              <View style={tw`mt-6`}>
                <View style={tw`flex-row items-center justify-between`}>
                  <Heading>Инструкции</Heading>
                  {query.data?.external_link ? (
                    <TouchableOpacity
                      onPress={() =>
                        query.data?.external_link &&
                        Linking.openURL(query.data?.external_link)
                      }
                      style={tw`rounded-full p-2 items-center justify-center`}>
                      <PlayCircleIcon
                        size={35}
                        color={theme.extend.colors.primary}
                      />
                    </TouchableOpacity>
                  ) : null}
                </View>
                <HtmlRenderer html={query.data?.instructions} />
              </View>
            </Container>
          </View>

          <RecipeNutritionInfo
            calories={query.data?.calories}
            protein={query.data?.protein}
            carbs={query.data?.carbohydrates}
            fat={query.data?.fats}
          />
        </ScrollView>

        <RecipeMoreSheet
          visible={isShareModalVisible}
          onClose={() => setIsShareModalVisible(false)}
          recipeId={query.data?.id}
          recipeTitle={query.data?.title}
          isPremium={query.data?.premium}
          recipeImage={typeof images?.[0] === 'string' ? images[0] : ''}
        />
      </>
    );
  }
};

export default Recipe;
