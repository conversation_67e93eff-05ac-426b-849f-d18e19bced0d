import { useScrollToTop } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { useQuery } from '@tanstack/react-query';
import React, { useCallback } from 'react';
import { ActivityIndicator, RefreshControl, View } from 'react-native';

import { theme } from '../../tailwind.config';
import ChallengeListItem from '../features/challenges/ChallengeListItem';
import ErrorMessage from '../features/ui/shared/ErrorMessage';
import Text from '../features/ui/shared/Text';
import { getChallenges } from '../utils/queries';
import tw from '../utils/tailwind';

export type Challenge = {
  id: number;
  name: string;
  duration: string;
  calories_per_day: string;
  about: string;
  external_link?: string;
  image: string;
  items: ChallengeItem[];
};

export type ChallengeItem = {
  id: number;
  name: string;
  meals: ChallengeItemMeal[];
};

export type ChallengeItemMeal = {
  id: number;
  title: string;
  recipe: {
    id: number;
    title: string;
    image: string;
    premium: boolean;
  };
};

const Challenges = () => {
  const listRef = React.useRef(null);

  const {
    data: challenges,
    isRefetching,
    isError,
    isSuccess,
    isLoading,
    error,
    refetch,
  } = useQuery(['challenges'], getChallenges);

  useScrollToTop(listRef);

  const renderItem = useCallback(
    ({ item }: { item: Challenge }) => (
      <View style={tw`mb-10`}>
        <ChallengeListItem {...item} />
      </View>
    ),
    [],
  );

  const ListEmptyComponent = useCallback(
    () => (
      <View style={tw`py-10 items-center`}>
        <Text style={tw`text-center`}>
          В този момент няма добавени хранителни режими.
        </Text>
      </View>
    ),
    [],
  );

  if (isError) {
    console.log(error);
    return <ErrorMessage />;
  }

  return (
    <View style={tw`flex-1 bg-white`}>
      {isLoading ? (
        <View style={tw`flex-1 items-center justify-center`}>
          <ActivityIndicator
            size={'large'}
            color={theme.extend.colors.primary}
          />
        </View>
      ) : null}

      {isSuccess && !isLoading ? (
        <View style={tw`flex-1`}>
          <FlashList
            ref={listRef}
            contentContainerStyle={tw`px-6 py-6`}
            showsVerticalScrollIndicator={false}
            estimatedItemSize={30}
            refreshControl={
              <RefreshControl refreshing={isRefetching} onRefresh={refetch} />
            }
            data={challenges}
            keyExtractor={item => item.id.toString()}
            renderItem={renderItem}
            ListEmptyComponent={ListEmptyComponent}
          />
        </View>
      ) : null}
    </View>
  );
};

export default Challenges;
