import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

function CrownIcon(props) {
  return (
    <Svg
      width={30}
      height={30}
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <Path
        d="M8.125 27.5h13.75m-10-10h6.25m2.75 6.225H9.125c-.525 0-1.113-.413-1.287-.913L2.662 8.338c-.737-2.075.126-2.713 1.9-1.438l4.875 3.487c.813.563 1.738.275 2.088-.637l2.2-5.862c.7-1.875 1.863-1.875 2.563 0l2.2 5.862c.35.912 1.274 1.2 2.075.637l4.575-3.262c1.95-1.4 2.887-.687 2.087 1.575l-5.05 14.137c-.188.476-.775.888-1.3.888v0z"
        stroke="#FBC12C"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}

export default CrownIcon;
