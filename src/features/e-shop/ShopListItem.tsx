import React from 'react';
import { Linking, TouchableOpacity, View } from 'react-native';

import RemoteImage from '@components/RemoteImage';
import { Product } from '../../utils/queries';
import tw from '../../utils/tailwind';
import Heading from '../ui/shared/Heading';

const ShopListItem = (product: Product) => {
  const { name, permalink, images, price } = product;

  const uri = images?.[0]?.src;

  const handlePress = async () => {
    const canOpenProduct = await Linking.canOpenURL(permalink);

    if (canOpenProduct) {
      await Linking.openURL(permalink);
    }
  };

  return (
    <TouchableOpacity onPress={handlePress} style={tw`px-6 my-6`}>
      <RemoteImage
        uri={uri}
        containerStyle={tw`w-full h-[400px] rounded`}
      />

      <View style={tw`flex-row items-center w-full mt-3`}>
        <Heading style={tw`flex-shrink flex-1`}>{name}</Heading>
        <Heading style={tw`w-24 text-right`}>{price} лв.</Heading>
      </View>
    </TouchableOpacity>
  );
};

export default ShopListItem;
