/**
 * Utility functions for handling dates in the meal planner
 */

/**
 * Get the start date of the week (Monday) for a given date
 * @param date The date to get the week start for
 * @returns The start date of the week as an ISO string (YYYY-MM-DD)
 */
export const getWeekStartDate = (date: Date): string => {
  const day = date.getDay();
  // Adjust for Sunday (0) to be treated as 7
  const diff = date.getDate() - day + (day === 0 ? -6 : 1);
  const monday = new Date(date);
  monday.setDate(diff);
  return formatDateToISOString(monday);
};

/**
 * Get an array of dates for a week starting from a given date
 * @param weekStartDate The start date of the week as an ISO string (YYYY-MM-DD)
 * @returns An array of 7 dates as ISO strings (YYYY-MM-DD)
 */
export const getWeekDates = (weekStartDate: string): string[] => {
  const startDate = new Date(weekStartDate);
  const weekDates: string[] = [];
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);
    weekDates.push(formatDateToISOString(date));
  }
  
  return weekDates;
};

/**
 * Get the next week's start date
 * @param currentWeekStartDate The current week's start date as an ISO string (YYYY-MM-DD)
 * @returns The next week's start date as an ISO string (YYYY-MM-DD)
 */
export const getNextWeekStartDate = (currentWeekStartDate: string): string => {
  const startDate = new Date(currentWeekStartDate);
  startDate.setDate(startDate.getDate() + 7);
  return formatDateToISOString(startDate);
};

/**
 * Get the previous week's start date
 * @param currentWeekStartDate The current week's start date as an ISO string (YYYY-MM-DD)
 * @returns The previous week's start date as an ISO string (YYYY-MM-DD)
 */
export const getPreviousWeekStartDate = (currentWeekStartDate: string): string => {
  const startDate = new Date(currentWeekStartDate);
  startDate.setDate(startDate.getDate() - 7);
  return formatDateToISOString(startDate);
};

/**
 * Format a date to ISO string (YYYY-MM-DD)
 * @param date The date to format
 * @returns The formatted date as an ISO string (YYYY-MM-DD)
 */
export const formatDateToISOString = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

/**
 * Format a date to a human-readable string (e.g., "Monday, May 1")
 * @param dateString The date as an ISO string (YYYY-MM-DD)
 * @returns The formatted date as a human-readable string
 */
export const formatDateToHumanReadable = (dateString: string): string => {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = { 
    weekday: 'long', 
    month: 'long', 
    day: 'numeric' 
  };
  return date.toLocaleDateString('bg-BG', options);
};

/**
 * Format a date to show only the day of the week
 * @param dateString The date as an ISO string (YYYY-MM-DD)
 * @returns The day of the week
 */
export const formatDateToDayOfWeek = (dateString: string): string => {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = { weekday: 'long' };
  return date.toLocaleDateString('bg-BG', options);
};

/**
 * Check if a date is today
 * @param dateString The date as an ISO string (YYYY-MM-DD)
 * @returns True if the date is today, false otherwise
 */
export const isToday = (dateString: string): boolean => {
  const today = formatDateToISOString(new Date());
  return dateString === today;
};

/**
 * Get the current week's start date
 * @returns The current week's start date as an ISO string (YYYY-MM-DD)
 */
export const getCurrentWeekStartDate = (): string => {
  return getWeekStartDate(new Date());
};

/**
 * Generate an array of week start dates for a given number of weeks
 * @param startFromDate The date to start from
 * @param numberOfWeeks The number of weeks to generate
 * @param direction The direction to generate weeks ('future' or 'past')
 * @returns An array of week start dates as ISO strings (YYYY-MM-DD)
 */
export const generateWeekStartDates = (
  startFromDate: Date,
  numberOfWeeks: number,
  direction: 'future' | 'past' = 'future'
): string[] => {
  const weekStartDates: string[] = [];
  const startDate = new Date(startFromDate);
  
  // Get the start of the week for the given date
  const firstWeekStartDate = getWeekStartDate(startDate);
  weekStartDates.push(firstWeekStartDate);
  
  // Generate the rest of the week start dates
  for (let i = 1; i < numberOfWeeks; i++) {
    const previousDate = weekStartDates[i - 1];
    const nextDate = direction === 'future' 
      ? getNextWeekStartDate(previousDate)
      : getPreviousWeekStartDate(previousDate);
    weekStartDates.push(nextDate);
  }
  
  return weekStartDates;
};
