import { produce } from 'immer';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { storage } from '@utils/storage';
import { Recipe } from '@features/recipes';

export type MealType = 'breakfast' | 'lunch' | 'dinner';

export interface MealPlan {
  id: string; // ISO date string for the week start (e.g., '2023-05-01')
  days: {
    [key: string]: { // ISO date string for the day (e.g., '2023-05-01')
      [mealType in MealType]?: {
        recipeId: number;
        title: string;
        image: string;
        premium: boolean;
      };
    };
  };
}

interface MealPlannerStore {
  mealPlans: MealPlan[];
  
  actions: {
    // Create a new meal plan for a specific week
    createMealPlan: (weekStartDate: string) => void;
    
    // Add a recipe to a specific day and meal type
    addRecipeToMealPlan: (
      weekStartDate: string,
      dayDate: string,
      mealType: MealType,
      recipe: Pick<Recipe, 'id' | 'title' | 'image' | 'premium'>
    ) => void;
    
    // Remove a recipe from a specific day and meal type
    removeRecipeFromMealPlan: (
      weekStartDate: string,
      dayDate: string,
      mealType: MealType
    ) => void;
    
    // Delete an entire meal plan
    deleteMealPlan: (weekStartDate: string) => void;
  };
}

export const useMealPlannerStore = create<MealPlannerStore>()(
  persist(
    (set) => ({
      mealPlans: [],
      
      actions: {
        createMealPlan: (weekStartDate) => {
          set(
            produce((draft) => {
              // Check if meal plan already exists
              const existingPlanIndex = draft.mealPlans.findIndex(
                (plan) => plan.id === weekStartDate
              );
              
              // If it doesn't exist, create a new one
              if (existingPlanIndex === -1) {
                draft.mealPlans.push({
                  id: weekStartDate,
                  days: {},
                });
              }
            })
          );
        },
        
        addRecipeToMealPlan: (weekStartDate, dayDate, mealType, recipe) => {
          set(
            produce((draft) => {
              // Find the meal plan
              const mealPlanIndex = draft.mealPlans.findIndex(
                (plan) => plan.id === weekStartDate
              );
              
              // If meal plan doesn't exist, create it
              if (mealPlanIndex === -1) {
                draft.mealPlans.push({
                  id: weekStartDate,
                  days: {
                    [dayDate]: {
                      [mealType]: {
                        recipeId: recipe.id,
                        title: recipe.title,
                        image: recipe.image,
                        premium: recipe.premium,
                      },
                    },
                  },
                });
                return;
              }
              
              // If the day doesn't exist in the meal plan, create it
              if (!draft.mealPlans[mealPlanIndex].days[dayDate]) {
                draft.mealPlans[mealPlanIndex].days[dayDate] = {};
              }
              
              // Add the recipe to the meal plan
              draft.mealPlans[mealPlanIndex].days[dayDate][mealType] = {
                recipeId: recipe.id,
                title: recipe.title,
                image: recipe.image,
                premium: recipe.premium,
              };
            })
          );
        },
        
        removeRecipeFromMealPlan: (weekStartDate, dayDate, mealType) => {
          set(
            produce((draft) => {
              // Find the meal plan
              const mealPlanIndex = draft.mealPlans.findIndex(
                (plan) => plan.id === weekStartDate
              );
              
              // If meal plan doesn't exist, return
              if (mealPlanIndex === -1) return;
              
              // If the day doesn't exist in the meal plan, return
              if (!draft.mealPlans[mealPlanIndex].days[dayDate]) return;
              
              // Remove the recipe from the meal plan
              delete draft.mealPlans[mealPlanIndex].days[dayDate][mealType];
              
              // If the day is now empty, remove it
              if (Object.keys(draft.mealPlans[mealPlanIndex].days[dayDate]).length === 0) {
                delete draft.mealPlans[mealPlanIndex].days[dayDate];
              }
              
              // If the meal plan is now empty, remove it
              if (Object.keys(draft.mealPlans[mealPlanIndex].days).length === 0) {
                draft.mealPlans.splice(mealPlanIndex, 1);
              }
            })
          );
        },
        
        deleteMealPlan: (weekStartDate) => {
          set(
            produce((draft) => {
              // Find the meal plan
              const mealPlanIndex = draft.mealPlans.findIndex(
                (plan) => plan.id === weekStartDate
              );
              
              // If meal plan doesn't exist, return
              if (mealPlanIndex === -1) return;
              
              // Remove the meal plan
              draft.mealPlans.splice(mealPlanIndex, 1);
            })
          );
        },
      },
    }),
    {
      name: 'veganna-meal-planner',
      storage: createJSONStorage(() => storage),
      partialize: (state) => ({ mealPlans: state.mealPlans }),
    }
  )
);

export const useMealPlannerActions = () =>
  useMealPlannerStore((state) => state.actions);
