import React from 'react';
import { View } from 'react-native';
import tw from '@utils/tailwind';
import Text from '@features/ui/shared/Text';
import { MealPlan, MealType } from '../meal-planner.store';
import { formatDateToDayOfWeek, formatDateToHumanReadable, isToday } from '../utils/date-utils';
import { MealSlot } from './MealSlot';

interface DailyMealPlanProps {
  day: string;
  weekStartDate: string;
  mealPlan?: MealPlan;
}

export const DailyMealPlan = ({ day, weekStartDate, mealPlan }: DailyMealPlanProps) => {
  const isCurrentDay = isToday(day);
  const dayName = formatDateToDayOfWeek(day);
  const fullDate = formatDateToHumanReadable(day);
  
  // Get the meals for this day from the meal plan
  const dayMeals = mealPlan?.days[day] || {};
  
  const mealTypes: MealType[] = ['breakfast', 'lunch', 'dinner'];
  
  return (
    <View style={tw`px-4 py-3 ${isCurrentDay ? 'bg-background' : ''}`}>
      <View style={tw`flex-row items-center mb-2`}>
        <Text style={tw`text-lg font-body-bold text-black`}>
          {dayName}
        </Text>
        <Text style={tw`text-sm text-gray-500 ml-2`}>
          {fullDate}
        </Text>
        {isCurrentDay && (
          <View style={tw`ml-2 px-2 py-0.5 bg-primary rounded-full`}>
            <Text style={tw`text-xs text-white`}>Днес</Text>
          </View>
        )}
      </View>
      
      <View style={tw`space-y-3`}>
        {mealTypes.map(mealType => (
          <MealSlot
            key={mealType}
            mealType={mealType}
            meal={dayMeals[mealType]}
            day={day}
            weekStartDate={weekStartDate}
          />
        ))}
      </View>
    </View>
  );
};
