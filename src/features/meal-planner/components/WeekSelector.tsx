import React, { useCallback } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { ChevronLeftIcon, ChevronRightIcon } from 'react-native-heroicons/outline';
import tw from '@utils/tailwind';
import Text from '@features/ui/shared/Text';

interface WeekSelectorProps {
  availableWeeks: string[];
  currentWeekStartDate: string;
  onWeekChange: (weekStartDate: string) => void;
}

export const WeekSelector = ({
  availableWeeks,
  currentWeekStartDate,
  onWeekChange
}: WeekSelectorProps) => {
  const currentWeekIndex = availableWeeks.indexOf(currentWeekStartDate);

  const handlePreviousWeek = useCallback(() => {
    if (currentWeekIndex > 0) {
      onWeekChange(availableWeeks[currentWeekIndex - 1]);
    }
  }, [currentWeekIndex, availableWeeks, onWeekChange]);

  const handleNextWeek = useCallback(() => {
    if (currentWeekIndex < availableWeeks.length - 1) {
      onWeekChange(availableWeeks[currentWeekIndex + 1]);
    }
  }, [currentWeekIndex, availableWeeks, onWeekChange]);

  // Format the week range for display (e.g., "May 1 - May 7")
  const weekRange = useCallback((weekStartDate: string) => {
    const startDate = new Date(weekStartDate);
    const endDate = new Date(weekStartDate);
    endDate.setDate(endDate.getDate() + 6);

    const startMonth = startDate.toLocaleDateString('bg-BG', { month: 'long' });
    const startDay = startDate.getDate();

    const endMonth = endDate.toLocaleDateString('bg-BG', { month: 'long' });
    const endDay = endDate.getDate();

    if (startMonth === endMonth) {
      return `${startDay} - ${endDay} ${startMonth}`;
    }

    return `${startDay} ${startMonth} - ${endDay} ${endMonth}`;
  }, []);

  return (
    <View style={tw`px-4 py-3 border-b border-gray-100 flex-row items-center justify-between`}>
      <TouchableOpacity
        onPress={handlePreviousWeek}
        disabled={currentWeekIndex === 0}
        style={tw`${currentWeekIndex === 0 ? 'opacity-30' : ''}`}
      >
        <ChevronLeftIcon size={24} color="#511D19" />
      </TouchableOpacity>

      <View style={tw`flex-1 items-center`}>
        <Text style={tw`text-base font-body-semibold text-black`}>
          {weekRange(currentWeekStartDate)}
        </Text>
      </View>

      <TouchableOpacity
        onPress={handleNextWeek}
        disabled={currentWeekIndex === availableWeeks.length - 1}
        style={tw`${currentWeekIndex === availableWeeks.length - 1 ? 'opacity-30' : ''}`}
      >
        <ChevronRightIcon size={24} color="#511D19" />
      </TouchableOpacity>
    </View>
  );
};
