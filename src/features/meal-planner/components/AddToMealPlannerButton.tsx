import React, { useState } from 'react';
import { TouchableOpacity } from 'react-native';
import { CalendarDaysIcon } from 'react-native-heroicons/outline';
import tw from '@utils/tailwind';
import Text from '@features/ui/shared/Text';
import { Recipe } from '@features/recipes';
import AddToMealPlannerModal from './AddToMealPlannerModal';

interface AddToMealPlannerButtonProps {
  recipe: Recipe;
  buttonStyle?: any;
  textStyle?: any;
  iconSize?: number;
  iconColor?: string;
  label?: string;
}

const AddToMealPlannerButton: React.FC<AddToMealPlannerButtonProps> = ({
  recipe,
  buttonStyle,
  textStyle,
  iconSize = 24,
  iconColor = '#FFFFFF',
  label = 'Add to meal planner',
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handleOpenModal = () => {
    setIsModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };

  return (
    <>
      <TouchableOpacity
        style={[tw`flex-row bg-black py-4 px-8 rounded-lg items-center justify-center`, buttonStyle]}
        onPress={handleOpenModal}
        activeOpacity={0.7}
      >
        <CalendarDaysIcon
          size={iconSize}
          color={iconColor}
          style={tw`mr-3`}
        />
        <Text
          style={[tw`text-white text-base font-body-bold text-center`, textStyle]}
        >
          {label}
        </Text>
      </TouchableOpacity>

      <AddToMealPlannerModal
        visible={isModalVisible}
        onClose={handleCloseModal}
        recipe={recipe}
      />
    </>
  );
};

export default AddToMealPlannerButton;
