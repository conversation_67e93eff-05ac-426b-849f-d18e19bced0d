import React, { useCallback } from 'react';
import { View, TouchableOpacity, Pressable } from 'react-native';
import { PlusIcon, XMarkIcon } from 'react-native-heroicons/outline';
import { useNavigation } from '@react-navigation/native';
import tw from '@utils/tailwind';
import Text from '@features/ui/shared/Text';
import { MealType, useMealPlannerActions } from '../meal-planner.store';
import RemoteImage from '@components/RemoteImage';
import { FALLBACK_IMAGE_URI } from '@utils/api';

interface MealSlotProps {
  mealType: MealType;
  meal?: {
    recipeId: number;
    title: string;
    image: string;
    premium: boolean;
  };
  day: string;
  weekStartDate: string;
}

// Translate meal types to Bulgarian
const mealTypeLabels: Record<MealType, string> = {
  breakfast: 'Закуска',
  lunch: 'Обяд',
  dinner: 'Вечеря',
};

export const MealSlot = ({ mealType, meal, day, weekStartDate }: MealSlotProps) => {
  const navigation = useNavigation<any>();
  const { removeRecipeFromMealPlan } = useMealPlannerActions();
  
  // Handle adding a recipe to this meal slot
  const handleAddRecipe = useCallback(() => {
    navigation.navigate('MealPlannerRecipeSelect', {
      mealType,
      day,
      weekStartDate,
    });
  }, [navigation, mealType, day, weekStartDate]);
  
  // Handle removing a recipe from this meal slot
  const handleRemoveRecipe = useCallback(() => {
    removeRecipeFromMealPlan(weekStartDate, day, mealType);
  }, [removeRecipeFromMealPlan, weekStartDate, day, mealType]);
  
  // Handle viewing a recipe
  const handleViewRecipe = useCallback(() => {
    if (meal) {
      navigation.navigate('Recipe', {
        id: meal.recipeId,
        title: meal.title,
        premium: meal.premium,
      });
    }
  }, [navigation, meal]);
  
  return (
    <View style={tw`border border-gray-200 rounded-lg overflow-hidden`}>
      {/* Meal type header */}
      <View style={tw`bg-gray-100 px-3 py-2`}>
        <Text style={tw`font-body-semibold text-black`}>
          {mealTypeLabels[mealType]}
        </Text>
      </View>
      
      {/* Meal content */}
      {meal ? (
        <Pressable onPress={handleViewRecipe}>
          <View style={tw`flex-row items-center p-2`}>
            {/* Recipe image */}
            <RemoteImage
              uri={meal.image || FALLBACK_IMAGE_URI}
              containerStyle={tw`h-16 w-16 rounded-md overflow-hidden`}
              imageStyle={tw`h-16 w-16`}
              resizeMode="cover"
            />
            
            {/* Recipe title */}
            <View style={tw`flex-1 ml-3`}>
              <Text 
                numberOfLines={2}
                style={tw`font-body-medium text-black`}
              >
                {meal.title}
              </Text>
            </View>
            
            {/* Remove button */}
            <TouchableOpacity
              onPress={handleRemoveRecipe}
              style={tw`p-2`}
            >
              <XMarkIcon size={20} color="#511D19" />
            </TouchableOpacity>
          </View>
        </Pressable>
      ) : (
        <TouchableOpacity
          onPress={handleAddRecipe}
          style={tw`flex-row items-center justify-center p-4`}
        >
          <PlusIcon size={20} color="#D6837B" />
          <Text style={tw`ml-2 text-primary font-body-medium`}>
            Добави рецепта
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};
