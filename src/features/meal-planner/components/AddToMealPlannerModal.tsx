import React, { useCallback, useMemo, useState } from 'react';
import {
  Modal,
  Pressable,
  TouchableOpacity,
  View,
  ScrollView,
} from 'react-native';
import { ChevronLeftIcon, ChevronRightIcon, XMarkIcon } from 'react-native-heroicons/outline';
import tw from '@utils/tailwind';
import Text from '@features/ui/shared/Text';
import { ActionButton } from '@features/ui/button';
import { Recipe } from '@features/recipes';
import { MealType, useMealPlannerActions } from '../meal-planner.store';
import {
  formatDateToHumanReadable,
  getCurrentWeekStartDate,
  getNextWeekStartDate,
  getPreviousWeekStartDate,
  getWeekDates,
} from '../utils/date-utils';
import { showToast } from '@utils/in-app-notifications';

interface AddToMealPlannerModalProps {
  visible: boolean;
  onClose: () => void;
  recipe: Recipe;
}

const AddToMealPlannerModal: React.FC<AddToMealPlannerModalProps> = ({
  visible,
  onClose,
  recipe,
}) => {
  // State for selected week and day
  const [currentWeekStartDate, setCurrentWeekStartDate] = useState(getCurrentWeekStartDate());
  const [selectedDay, setSelectedDay] = useState<string | null>(null);
  const [selectedMealType, setSelectedMealType] = useState<MealType | null>(null);
  
  // Get the days for the current week
  const weekDays = useMemo(() => 
    getWeekDates(currentWeekStartDate),
  [currentWeekStartDate]);
  
  // Get meal planner actions
  const { addRecipeToMealPlan } = useMealPlannerActions();
  
  // Handle week navigation
  const handlePreviousWeek = useCallback(() => {
    setCurrentWeekStartDate(getPreviousWeekStartDate(currentWeekStartDate));
    setSelectedDay(null);
  }, [currentWeekStartDate]);
  
  const handleNextWeek = useCallback(() => {
    setCurrentWeekStartDate(getNextWeekStartDate(currentWeekStartDate));
    setSelectedDay(null);
  }, [currentWeekStartDate]);
  
  // Format the week range for display (e.g., "Week 20, 2025")
  const weekTitle = useCallback((weekStartDate: string) => {
    const date = new Date(weekStartDate);
    const weekNumber = Math.ceil((date.getDate() + date.getDay()) / 7);
    const year = date.getFullYear();
    return `Week ${weekNumber}, ${year}`;
  }, []);
  
  // Handle day selection
  const handleDaySelect = useCallback((day: string) => {
    setSelectedDay(day);
    setSelectedMealType(null);
  }, []);
  
  // Handle meal type selection
  const handleMealTypeSelect = useCallback((mealType: MealType) => {
    setSelectedMealType(mealType);
  }, []);
  
  // Handle adding recipe to meal plan
  const handleAddToMealPlanner = useCallback(() => {
    if (!selectedDay || !selectedMealType) return;
    
    addRecipeToMealPlan(
      currentWeekStartDate,
      selectedDay,
      selectedMealType,
      {
        recipeId: recipe.id,
        title: recipe.title,
        image: recipe.image,
        premium: recipe.premium,
      }
    );
    
    showToast({
      type: 'info',
      description: 'Рецептата е добавена в седмичния план',
    });
    
    onClose();
  }, [currentWeekStartDate, selectedDay, selectedMealType, recipe, addRecipeToMealPlan, onClose]);
  
  // Translate meal types to Bulgarian
  const mealTypeLabels: Record<MealType, string> = {
    breakfast: 'Закуска',
    lunch: 'Обяд',
    dinner: 'Вечеря',
  };
  
  // Render meal type selection if a day is selected
  const renderMealTypeSelection = () => {
    if (!selectedDay) return null;
    
    const mealTypes: MealType[] = ['breakfast', 'lunch', 'dinner'];
    
    return (
      <View style={tw`mt-4`}>
        <Text style={tw`text-center mb-4 font-body-semibold`}>
          Изберете хранене
        </Text>
        <View style={tw`space-y-3`}>
          {mealTypes.map((mealType) => (
            <TouchableOpacity
              key={mealType}
              style={tw`flex-row items-center justify-between p-3 border rounded-lg ${
                selectedMealType === mealType ? 'border-primary bg-primary/10' : 'border-gray-200'
              }`}
              onPress={() => handleMealTypeSelect(mealType)}
            >
              <Text style={tw`font-body-medium ${
                selectedMealType === mealType ? 'text-primary' : 'text-black'
              }`}>
                {mealTypeLabels[mealType]}
              </Text>
              <View style={tw`h-5 w-5 rounded-full border ${
                selectedMealType === mealType ? 'border-primary bg-primary' : 'border-gray-300'
              } items-center justify-center`}>
                {selectedMealType === mealType && (
                  <View style={tw`h-2 w-2 rounded-full bg-white`} />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };
  
  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={tw`flex-1 bg-black/50 justify-center items-center`}>
        <View style={tw`bg-white rounded-xl w-11/12 max-h-[80%] overflow-hidden`}>
          {/* Header */}
          <View style={tw`flex-row items-center justify-between p-4 border-b border-gray-200`}>
            <TouchableOpacity onPress={onClose} style={tw`p-1`}>
              <XMarkIcon size={24} color="#000" />
            </TouchableOpacity>
            <Text style={tw`text-lg font-body-bold text-center`}>
              Add to meal planner
            </Text>
            <View style={tw`w-6`} />
          </View>
          
          {/* Week selector */}
          <View style={tw`flex-row items-center justify-between p-4 border-b border-gray-200`}>
            <TouchableOpacity onPress={handlePreviousWeek} style={tw`p-1`}>
              <ChevronLeftIcon size={24} color="#000" />
            </TouchableOpacity>
            <Text style={tw`text-base font-body-semibold`}>
              {weekTitle(currentWeekStartDate)}
            </Text>
            <TouchableOpacity onPress={handleNextWeek} style={tw`p-1`}>
              <ChevronRightIcon size={24} color="#000" />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={tw`max-h-[400px]`} contentContainerStyle={tw`p-4`}>
            {/* Day selection */}
            <Text style={tw`text-center mb-4 font-body-semibold`}>
              Choose on what day to add this recipe
            </Text>
            <View style={tw`space-y-3`}>
              {weekDays.map((day) => (
                <TouchableOpacity
                  key={day}
                  style={tw`flex-row items-center justify-between p-3 border rounded-lg ${
                    selectedDay === day ? 'border-primary bg-primary/10' : 'border-gray-200'
                  }`}
                  onPress={() => handleDaySelect(day)}
                >
                  <Text style={tw`font-body-medium ${
                    selectedDay === day ? 'text-primary' : 'text-black'
                  }`}>
                    {formatDateToHumanReadable(day)}
                  </Text>
                  <View style={tw`h-5 w-5 rounded-full border ${
                    selectedDay === day ? 'border-primary bg-primary' : 'border-gray-300'
                  } items-center justify-center`}>
                    {selectedDay === day && (
                      <View style={tw`h-2 w-2 rounded-full bg-white`} />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
            
            {/* Meal type selection */}
            {renderMealTypeSelection()}
          </ScrollView>
          
          {/* Footer */}
          <View style={tw`p-4 border-t border-gray-200`}>
            <ActionButton
              label="Add to meal planner"
              disabled={!selectedDay || !selectedMealType}
              onPress={handleAddToMealPlanner}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default AddToMealPlannerModal;
