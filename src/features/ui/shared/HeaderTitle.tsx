import React, { FC } from 'react';
import { TextProps } from 'react-native';
import tw from '../../../utils/tailwind';
import Text from './Text';

const HeaderTitle: FC<TextProps> = props => {
  const { children, style, ...rest } = props;
  return (
    <Text
      style={[
        tw`font-body-bold text-base leading-tight text-center max-w-[250px]`,
        style,
      ]}
      {...rest}
      ellipsizeMode="tail"
      numberOfLines={2}>
      {children}
    </Text>
  );
};

export default HeaderTitle;
