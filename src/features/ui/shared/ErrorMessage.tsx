import React from 'react';
import { View } from 'react-native';
import { ExclamationTriangleIcon } from 'react-native-heroicons/outline';
import { theme } from '../../../../tailwind.config';
import tw from '../../../utils/tailwind';
import Text from './Text';

const ErrorMessage = ({ children }: { children?: any }) => {
  return (
    <View style={tw`bg-white flex-1 items-center justify-center`}>
      <ExclamationTriangleIcon size={35} color={theme.extend.colors.primary} />
      <Text style={tw`text-center px-8 mt-4`}>
        {children
          ? children
          : 'Възникна грешка при извличане на данните. Моля опитайте по-късно.'}
      </Text>
    </View>
  );
};

export default ErrorMessage;
