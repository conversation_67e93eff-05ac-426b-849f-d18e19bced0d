import React, { FC } from 'react';
import {
  Text as ReactNativeText,
  TextProps as ReactNativeTextProps,
} from 'react-native';
import tw from '../../../utils/tailwind';

const Text: FC<ReactNativeTextProps> = props => {
  const { children, style, ...rest } = props;
  return (
    <ReactNativeText
      style={[
        tw`text-base text-black`,
        style,
        {
          fontFamily: 'Comfortaa-Regular',
        },
      ]}
      {...rest}>
      {children}
    </ReactNativeText>
  );
};

export default Text;
