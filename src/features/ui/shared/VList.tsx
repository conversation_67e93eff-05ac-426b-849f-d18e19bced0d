import { FlatList } from 'react-native';
import React, { PropsWithChildren } from 'react';
import tw from '@utils/tailwind';

const VList = (props: PropsWithChildren) => {
  return (
    <FlatList
      data={[]}
      renderItem={() => null}
      ListHeaderComponent={<>{props.children}</>}
      keyExtractor={() => Math.random().toString()}
      style={tw`flex-1`}
      contentContainerStyle={tw`pb-10 bg-white`}
      nestedScrollEnabled
    />
  );
};

export default VList;
