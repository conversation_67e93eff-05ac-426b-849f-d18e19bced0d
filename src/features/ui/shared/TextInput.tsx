import React, { FC } from 'react';
import {
  TextInput as RNInput,
  View,
  TextInputProps as RnInputProps,
  ReturnKeyType,
  NativeSyntheticEvent,
  TextInputSubmitEditingEventData,
} from 'react-native';
import Text from './Text';
import { Control, useController } from 'react-hook-form';
import { InputError } from '@features/ui/shared';
import tw from '@utils/tailwind';

type TextInputProps = {
  name: string;
  label?: string;
  placeholder?: string;
  control: Control<any>;
  defaultValue?: string;
  rules?: object;
  multiline?: boolean;
  onChange?: (e: any) => void;
  secureTextEntry?: boolean;
  disabled?: boolean;
  returnKeyType?: ReturnKeyType;
  onSubmitEditing?: (e: NativeSyntheticEvent<TextInputSubmitEditingEventData>) => void;
};

const TextInput: FC<TextInputProps> = ({
  control,
  name,
  placeholder,
  label,
  defaultValue,
  rules,
  multiline,
  onChange,
  secureTextEntry,
  disabled,
  returnKeyType,
  onSubmitEditing
}) => {
  const { field, fieldState } = useController({
    control,
    name,
    defaultValue,
    rules,
  });

  const hasError = fieldState.error;

  return (
    <View>
      {label && (
        <Text style={tw`mb-1 text-sm font-body-semibold text-black`}>
          {label}
        </Text>
      )}
      <RNInput
        {...field}
        style={[
          tw`border rounded-md px-3 py-5 text-neutral-700`,
          hasError ? tw`border-red-500` : tw`border-neutral-300`,
          multiline ? tw`h-24` : tw`h-auto`,
          disabled ? tw`bg-neutral-100 text-neutral-400` : tw`bg-white`,
        ]}
        placeholderTextColor="rgba(0,0,0,.4)"
        placeholder={placeholder}
        autoCapitalize="none"
        value={field.value}
        returnKeyType={returnKeyType}
        onSubmitEditing={onSubmitEditing}
        {...(onChange
          ? { onChangeText: onChange }
          : {
              onChangeText: field.onChange,
            })}
        {...(multiline && {
          multiline,
        })}
        {...(secureTextEntry && {
          secureTextEntry: true,
        })}
        {...(disabled && {
          editable: false,
          selectTextOnFocus: false,
        })}
      />
      {hasError && <InputError>{fieldState.error?.message}</InputError>}
    </View>
  );
};

export default TextInput;
