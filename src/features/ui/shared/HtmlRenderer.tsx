import React from 'react';
import { useWindowDimensions } from 'react-native';
import RenderHTML from 'react-native-render-html';

import tw from '../../../utils/tailwind';

const tagStyles = {
  body: tw`text-base text-gray-800`,
  h1: tw`text-lg text-black font-body-regular`,
  h2: tw`text-base text-black font-body-regular`,
  h3: tw`text-xl`,
  h4: tw`text-lg`,
  a: tw`no-underline text-primary font-body-semibold`,
  p: tw`flex-row text-sm text-gray-600 font-body-regular`,
  ul: tw`text-gray-600 text-sm font-body-regular`,
  ol: tw`text-gray-600 text-sm font-body-regular mb-3`,
  li: tw`text-gray-600 text-sm font-body-regular mb-3`,
  i: tw`italic`,
  em: tw`italic`,
  strong: tw`font-body-bold`,
  b: tw`font-body-bold`,
};

const renderersProps = {
  ol: {
    markerBoxStyle: {
      paddingTop: 5,
      paddingRight: 0,
    },
    markerTextStyle: {
      color: '#D6817A',
      fontFamily: 'AmaticSC-Bold',
      fontSize: 35,
      lineHeight: 35,
    },
  },
};

const HtmlRenderer = ({ html, ...props }) => {
  const { width } = useWindowDimensions();

  return (
    <RenderHTML
      renderersProps={renderersProps}
      tagsStyles={tagStyles}
      contentWidth={width}
      systemFonts={['Comfortaa-Regular', 'AmaticSC-Bold']}
      source={{ html }}
      enableExperimentalBRCollapsing
      enableExperimentalGhostLinesPrevention
      {...props}
    />
  );
};

export default React.memo(HtmlRenderer);
