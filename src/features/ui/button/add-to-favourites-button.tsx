import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { Pressable, View } from 'react-native';
import HeartIcon from 'react-native-heroicons/outline/HeartIcon';
import Reanimated, {
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withSpring,
} from 'react-native-reanimated';

import { showToast } from '@utils/in-app-notifications';
import { theme } from 'tailwind.config';
import { useDataStore } from '../../../utils/store';
import tw from '../../../utils/tailwind';

interface AddToFavouritesButtonProps {
  recipeId: number;
  recipeTitle: string;
  premium: boolean;
  bgSize?: number;
  iconSize?: number;
}

export const AddToFavouritesButton: React.FC<AddToFavouritesButtonProps> = ({
  recipeId,
  recipeTitle,
  premium,
  bgSize = 9,
  iconSize = 22,
}: {
  recipeId: number;
  recipeTitle: string;
  premium: boolean;
  bgSize?: number;
  iconSize?: number;
}) => {
  const navigation = useNavigation();
  const scale = useSharedValue(1);
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const favourites = useDataStore(state => state.favourites);
  const toggleFavourite = useDataStore(state => state.toggleFavourite);

  const isFavourite = React.useMemo(
    () => Object.keys(favourites).includes(`${recipeId}`),
    [favourites, recipeId],
  );

  const animateHeart = () => {
    'worklet';
    scale.value = withSequence(
      withSpring(1.2, { stiffness: 120, damping: 10 }),
      withSpring(1, { stiffness: 120, damping: 10 }),
    );
  };

  const handleAddToFavourites = React.useCallback(() => {
    animateHeart();
    toggleFavourite({ recipeId, recipeTitle, premium });
    showToast({
      type: 'info',
      description: isFavourite
        ? 'Рецептата е премахната от любими'
        : 'Рецептата е добавена в любими 😋',
      onPress: () => navigation.navigate('Favourites' as never),
    });
  }, [
    toggleFavourite,
    isFavourite,
    animateHeart,
    recipeId,
    recipeTitle,
    premium,
  ]);

  return (
    <Pressable onPress={handleAddToFavourites}>
      <View
        style={tw`w-${bgSize} h-${bgSize} rounded-full bg-white/80 items-center justify-center`}>
        <Reanimated.View style={animatedStyle}>
          <HeartIcon
            stroke={isFavourite ? '#F35F5F' : theme.extend.colors.primary}
            size={iconSize}
            fill={isFavourite ? '#F35F5F' : 'transparent'}
            strokeWidth={1.8}
          />
        </Reanimated.View>
      </View>
    </Pressable>
  );
};
