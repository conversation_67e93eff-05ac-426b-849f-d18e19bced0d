import React from 'react';
import { ActivityIndicator, StyleProp, TextStyle } from 'react-native';
import { Pressable, View, Text, PressableProps } from 'react-native';
import tw from '@utils/tailwind';

interface ActionButtonProps extends PressableProps {
  label: string;
  textStyle?: StyleProp<TextStyle>;
  loading?: boolean;
}

export const ActionButton = (props: ActionButtonProps) => {
  const { label, textStyle, loading, ...rest } = props;

  return (
    <Pressable
      style={({ pressed }) => [
        tw`w-full px-6`,
        tw.style(rest.disabled ? 'opacity-50' : 'opacity-100'),
        pressed
          ? {
              transform: [{ translateY: 2 }],
            }
          : {},
      ]}
      {...rest}>
      <View
        style={tw`bg-primary w-full py-4 px-10 rounded-lg items-center justify-center`}>
        {loading ? (
          <ActivityIndicator size={'small'} color="#fff" />
        ) : (
          <Text
            style={[
              tw`text-white text-base font-body-bold text-center text-sm`,
              textStyle,
            ]}>
            {label}
          </Text>
        )}
      </View>
    </Pressable>
  );
};
