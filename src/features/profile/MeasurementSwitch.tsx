import React from 'react';
import { TouchableWithoutFeedback, View } from 'react-native';
import Animated, {
  interpolateColor,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { theme } from '../../../tailwind.config';
import { MeasurementSystem, useDataStore } from '../../utils/store';
import tw from '../../utils/tailwind';
import Text from '../ui/shared/Text';

const MeasurementSwitch = () => {
  const [system, setSystem] = useDataStore(state => [
    state.system,
    state.setSystem,
  ]);

  const metricTextColorProgress = useDerivedValue(() => {
    return withTiming(system === 'metric' ? 0 : 1);
  });

  const imperialTextColorProgress = useDerivedValue(() => {
    return withTiming(system === 'imperial' ? 0 : 1);
  });

  const translateX = useSharedValue(system === 'metric' ? 5 : 95);

  const handleSystemChange = React.useCallback(
    (_system: MeasurementSystem) => {
      setSystem(_system);
      if (_system === 'imperial') {
        translateX.value = 95;
      } else {
        translateX.value = 5;
      }
    },
    [setSystem, translateX],
  );

  const bgElStyles = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: withSpring(translateX.value, {
          mass: 0.9,
          damping: 13,
          stiffness: 90,
        }),
      },
    ],
  }));

  const metricTextStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      metricTextColorProgress.value,
      [0, 1],
      [theme.extend.colors.black, '#fff'],
    );

    return { color };
  });

  const imperialTextStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      imperialTextColorProgress.value,
      [0, 1],
      [theme.extend.colors.black, '#fff'],
    );

    return { color };
  });

  return (
    <View
      style={[
        tw`bg-white rounded-lg mt-4 flex-row justify-between items-center`,
      ]}>
      <Text style={tw`text-sm font-body-semibold text-slate-600`}>
        Мерни единици
      </Text>
      <View
        style={[
          tw`flex-row justify-between bg-primary rounded-lg h-9 relative max-w-45`,
        ]}>
        <Animated.View
          style={[
            tw`bg-white absolute w-20 shadow shadow-sm h-6 rounded-lg top-1.5 bottom-1.5`,
            bgElStyles,
          ]}
        />
        <TouchableWithoutFeedback onPress={() => handleSystemChange('metric')}>
          <View style={[tw`py-1 justify-center items-center w-[50%]`]}>
            <Animated.Text
              style={[
                tw`text-sm font-body-semibold text-center`,
                metricTextStyle,
                {
                  fontFamily: 'Comfortaa-SemiBold',
                  fontSize: 12.5,
                },
              ]}>
              грамове
            </Animated.Text>
          </View>
        </TouchableWithoutFeedback>
        <TouchableWithoutFeedback
          onPress={() => handleSystemChange('imperial')}>
          <View style={[tw`py-1 justify-center items-center w-[50%]`]}>
            <Animated.Text
              style={[
                tw`text-sm font-body-semibold text-center`,
                imperialTextStyle,
                {
                  fontFamily: 'Comfortaa-SemiBold',
                  fontSize: 12.5,
                },
              ]}>
              чаши
            </Animated.Text>
          </View>
        </TouchableWithoutFeedback>
      </View>
    </View>
  );
};

export default MeasurementSwitch;
