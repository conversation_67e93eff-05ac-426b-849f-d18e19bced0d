import React from 'react';
import { ActivityIndicator, Alert, TouchableOpacity, View } from 'react-native';
import { axiosInstance, BASE_URL } from '../../utils/api';
import tw from '../../utils/tailwind';
import Container from '../ui/shared/Container';
import Text from '../ui/shared/Text';
import { theme } from '../../../tailwind.config';
import { logout } from '../../utils/auth-actions';

const DeleteProfile = () => {
  const [isLoading, setIsLoading] = React.useState(false);

  const deleteProfile = () => {
    Alert.alert(
      'Внимание!',
      'Изтривайки акаунта, ще загубите абонамента си, ако вече сте закупили такъв!',
      [
        {
          text: 'Изтрий акаунта',
          onPress: async () => {
            try {
              setIsLoading(true);

              await axiosInstance.delete(BASE_URL + '/api/me');

              Alert.alert('Успешно изтрихте профила си!', undefined, [
                {
                  text: 'ОК',
                  onPress: async () => {
                    setIsLoading(false);
                    await logout();
                  },
                },
              ]);
            } catch (error) {
              console.log(error);
              setIsLoading(false);
              Alert.alert('Възникна грешка при изтриване на профила!');
            }
          },
          style: 'destructive',
        },
        {
          text: 'Откажи',
          style: 'cancel',
        },
      ],
    );
  };

  return (
    <Container style={tw`w-full items-center mt-10`}>
      {isLoading ? (
        <ActivityIndicator size="large" color={theme.extend.colors.primary} />
      ) : (
        <TouchableOpacity onPress={deleteProfile}>
          <View
            style={tw`bg-white border-2 border-red-400 rounded-md px-4 py-4 items-center`}>
            <Text style={tw`font-body-bold text-red-800 text-[15px]`}>
              Изтриване на профила
            </Text>
          </View>
        </TouchableOpacity>
      )}
    </Container>
  );
};

export default DeleteProfile;
