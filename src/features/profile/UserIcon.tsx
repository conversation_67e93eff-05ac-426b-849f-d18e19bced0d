import { useNavigation, useFocusEffect } from '@react-navigation/native';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Cog6ToothIcon } from 'react-native-heroicons/solid';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';

import { theme } from '../../../tailwind.config';
import tw from '../../utils/tailwind';

const UserIcon = () => {
  const navigation = useNavigation<any>();
  const rotation = useSharedValue(0);

  // Animated style for rotation
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }));

  // Handle navigation to Settings with rotation animation
  const handlePress = () => {
    rotation.value = withTiming(180, { duration: 250 });
    navigation.navigate('Settings');
  };

  // Listen for screen focus to animate back when returning from Settings
  useFocusEffect(
    React.useCallback(() => {
      // When this screen comes back into focus, rotate back to 0
      rotation.value = withTiming(0, { duration: 250 });
    }, [rotation])
  );

  return (
    <TouchableOpacity onPress={handlePress} style={tw`mt-1`}>
      <Animated.View style={animatedStyle}>
        <Cog6ToothIcon size={30} color={theme.extend.colors.secondary} />
      </Animated.View>
    </TouchableOpacity>
  );
};

export default UserIcon;
