import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Cog6ToothIcon } from 'react-native-heroicons/solid';

import { theme } from '../../../tailwind.config';
import tw from '../../utils/tailwind';

const UserIcon = () => {
  const navigation = useNavigation<any>();

  return (
    <TouchableOpacity
      onPress={() => navigation.navigate('Settings')}
      style={tw`mt-1`}>
      <Cog6ToothIcon size={30} color={theme.extend.colors.secondary} />
    </TouchableOpacity>
  );
};

export default UserIcon;
