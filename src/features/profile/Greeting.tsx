import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { useMe } from '../../utils/hooks';
import tw from '../../utils/tailwind';
import Heading from '../ui/shared/Heading';
import Text from '../ui/shared/Text';

const Greeting = () => {
  const [greeting, setGreeting] = useState('');
  const { data: me } = useMe();
  const navigation = useNavigation<any>();

  useEffect(() => {
    const hour = new Date().getHours();

    if (hour >= 0 && hour < 12) {
      setGreeting('Добро утро');
    } else if (hour >= 12 && hour < 17) {
      setGreeting('Добър ден');
    } else {
      setGreeting('Добър вечер');
    }
  }, []);

  const name = React.useMemo(() => me?.name?.split(' ')[0], [me?.name]);

  return (
    <View style={tw`flex-shrink`}>
      <Heading style={tw`text-4xl`}>
        {greeting}
        {name ? `, ${name}` : null}
      </Heading>
      <TouchableOpacity onPress={() => navigation.navigate('Challenges')}>
        <Text style={tw`mt-2 mb-6 text-sm text-black`}>
          Опитай седмично веган предизвикателство! →
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default Greeting;
