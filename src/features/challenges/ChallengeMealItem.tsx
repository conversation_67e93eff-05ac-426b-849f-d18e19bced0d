import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { LockClosedIcon } from 'react-native-heroicons/solid';

import { theme } from '../../../tailwind.config';
import RemoteImage from '@components/RemoteImage';
import { ChallengeItemMeal } from '../../screens/Challenges';
import { useDataStore } from '../../utils/store';
import tw from '../../utils/tailwind';

const ChallengeMealItem = ({
  meal,
  onPress,
}: {
  meal: ChallengeItemMeal;
  onPress: (meal: ChallengeItemMeal) => Promise<void>;
}) => {
  const isUserPremium = useDataStore(state => state.isUserPremium);
  // Premium users see all meals without any lock icons.
  // Non-premium users will only see the lock on premium recipes.
  const shouldShowLock = !isUserPremium && meal.recipe.premium;

  return (
    <TouchableOpacity style={tw`mb-6`} onPress={() => onPress(meal)}>
      <View style={tw`flex-row items-center`}>
        <RemoteImage
          uri={meal.recipe.image}
          containerStyle={tw`h-16 w-16 rounded-lg`}
          resizeMode="cover"
        />
        <View style={tw`justify-center ml-4 flex-shrink`}>
          <Text style={tw`text-black text-lg font-body-bold`}>
            {meal.title}
          </Text>
          <Text
            style={tw`text-slate-500 text-[13px] leading-snug font-body-regular pr-4 flex-shrink`}>
            {meal.recipe.title}
          </Text>
        </View>
        {shouldShowLock && (
          <LockClosedIcon
            style={tw`ml-auto`}
            color={theme.extend.colors.black}
          />
        )}
      </View>
    </TouchableOpacity>
  );
};

export default ChallengeMealItem;
