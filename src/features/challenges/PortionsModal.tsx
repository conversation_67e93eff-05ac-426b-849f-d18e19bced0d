import React from 'react';
import { Modal, View } from 'react-native';
import { useDataStore } from '../../utils/store';
import tw from '../../utils/tailwind';
import Container from '../ui/shared/Container';
import Heading from '../ui/shared/Heading';
import { RecipePortionControls } from "@features/recipes"
import Text from '../ui/shared/Text';

const PortionsModal = ({ setVisible, visible, mealPlanTitle, challengeId }) => {
  const challengePortion = useDataStore(
    state => state.challengePortions[challengeId],
  );

  const setChallengePortion = useDataStore(state => state.setChallengePortion);

  const [count, setCount] = React.useState(challengePortion || 1);

  React.useEffect(() => {
    if (count) {
      setChallengePortion(challengeId, count);
    }
  }, [setChallengePortion, challengeId, count]);

  return (
    <Modal
      animationType="slide"
      presentationStyle="formSheet"
      visible={visible}
      onRequestClose={() => {
        setVisible(!visible);
      }}>
      <View style={tw`bg-white flex-1`}>
        <Container>
          <Heading style={tw`text-center mt-10 text-4xl`}>
            Настройки на порциите
          </Heading>
          <Text style={tw`text-sm my-4 text-center`}>
            Избора е валиден за хранителен режим "{mealPlanTitle}"
          </Text>

          <View style={tw`flex-row items-center justify-between mt-4`}>
            <Text>
              Порции за {count} {count > 1 ? 'човека' : 'човек'}
            </Text>
            <RecipePortionControls count={count} setCount={setCount} />
          </View>
        </Container>
      </View>
    </Modal>
  );
};

export default PortionsModal;
