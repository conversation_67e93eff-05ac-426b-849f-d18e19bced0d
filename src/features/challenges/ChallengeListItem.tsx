import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { TouchableWithoutFeedback, View } from 'react-native';
import { ChevronRightIcon } from 'react-native-heroicons/outline';

import RemoteImage from '@components/RemoteImage';
import { theme } from '../../../tailwind.config';
import { Challenge } from '../../screens/Challenges';
import tw from '../../utils/tailwind';
import Text from '../ui/shared/Text';

const ChallengeListItem = ({ id, name, duration, image }: Challenge) => {
  const navigation = useNavigation<any>();

  const navigateToChallenge = React.useCallback(() => {
    if (!id) {
      return;
    }
    navigation.navigate('Challenge', {
      challengeId: id,
    });
  }, [id, navigation]);

  return (
    <TouchableWithoutFeedback onPress={navigateToChallenge}>
      <View style={tw`shadow bg-white rounded-lg`}>
        <RemoteImage
          uri={image}
          containerStyle={tw`h-70 rounded-t-lg w-full`}
        />
        <View
          style={tw`p-4 border-t border-primary bg-background/50 rounded-b-lg flex-row items-center justify-between`}>
          <View style={tw`flex-shrink`}>
            <Text
              style={tw`flex-shrink font-body-bold leading-snug mb-1 text-base`}>
              {name}
            </Text>
            <Text style={tw`flex-shrink text-base text-slate-600`}>
              {duration}
            </Text>
          </View>

          <ChevronRightIcon
            color={theme.extend.colors.black}
            size={28}
            strokeWidth={2}
          />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default ChallengeListItem;
