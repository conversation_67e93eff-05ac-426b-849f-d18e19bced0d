import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { ChevronRightIcon } from 'react-native-heroicons/outline';
import { theme } from '../../../tailwind.config';
import tw from '../../utils/tailwind';
import Heading from '../ui/shared/Heading';

const ChallengeItemRow = ({ name, onPress }) => {
  return (
    <TouchableOpacity activeOpacity={0.7} onPress={onPress}>
      <View
        style={tw`flex-row items-center justify-between py-4 bg-background px-6 border-b border-secondary/30`}>
        <Heading style={tw`text-4xl`}>{name}</Heading>
        <ChevronRightIcon
          size={30}
          strokeWidth={2}
          color={theme.extend.colors.black}
        />
      </View>
    </TouchableOpacity>
  );
};

export default ChallengeItemRow;
