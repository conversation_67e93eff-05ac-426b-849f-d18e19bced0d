import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { XMarkIcon } from 'react-native-heroicons/outline';
import tw from '@utils/tailwind';
import Text from '@features/ui/shared/Text';

export const RecipeFilterBadge = ({ label, active = false, onPress }) => {
  const viewStyles = React.useMemo(
    () =>
      active
        ? 'bg-primary border border-primary'
        : 'bg-background border border-primary',
    [active],
  );

  const textStyles = React.useMemo(
    () => (active ? 'text-white mr-1' : 'text-black'),
    [active],
  );

  return (
    <TouchableOpacity onPress={onPress} style={tw`mr-2 mt-2`}>
      <View
        style={[
          tw`flex-row items-center rounded-full py-1 px-3`,
          tw.style(viewStyles),
        ]}>
        <Text style={[tw`flex-shrink`, tw.style(textStyles)]}>{label}</Text>

        {active ? <XMarkIcon size={18} color={'#fff'} /> : null}
      </View>
    </TouchableOpacity>
  );
};
