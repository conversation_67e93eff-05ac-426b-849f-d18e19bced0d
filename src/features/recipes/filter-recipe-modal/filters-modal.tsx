import { useQuery } from '@tanstack/react-query';
import React from 'react';
import {
  ActivityIndicator,
  Modal,
  Pressable,
  ScrollView,
  View,
} from 'react-native';
import { XMarkIcon } from 'react-native-heroicons/outline';
import { theme } from '../../../../tailwind.config';

import { getCategories, getTags } from '@utils/queries';
import tw from '@utils/tailwind';

import Container from '@features/ui/shared/Container';
import Heading from '@features/ui/shared/Heading';
import Text from '@features/ui/shared/Text';

import {
  MultiSelectFilter,
  useRecipeFilterActions,
  useRecipeFilterStore,
} from '@features/recipes';

export type FiltersOnChange = ({}) => void;

export const FiltersModal = () => {
  const actions = useRecipeFilterActions();
  const isVisible = useRecipeFilterStore(state => state.isFilterModalOpen);
  const tagIds = useRecipeFilterStore(state => state.tags);
  const categoryIds = useRecipeFilterStore(state => state.categories);

  // const { data: categories, isLoading: categoriesLoading } = useQuery(
  //   ['categories', tagIds],
  //   getCategories,
  //   {
  //     enabled: isVisible,
  //   },
  // );

  const { data: categories, isLoading: categoriesLoading } = useQuery(
    ['categories', tagIds],
    getCategories,
    {
      enabled: isVisible,
    },
  );

  const { data: tags, isLoading: tagsLoading } = useQuery(
    ['tags', categoryIds],
    getTags,
    {
      enabled: isVisible,
    },
  );

  const isLoading = categoriesLoading && tagsLoading;

  return (
    <Modal
      animationType={'slide'}
      visible={isVisible}
      presentationStyle={'formSheet'}
      onRequestClose={actions.closeFilterModal}>
      <View style={tw`flex-1 bg-white`}>
        <ScrollView>
          <Pressable
            onPress={actions.closeFilterModal}
            style={tw`p-3  items-center justify-center self-end`}>
            <XMarkIcon size={30} color={theme.extend.colors.primary} />
          </Pressable>
          <View>
            <Container>
              <View
                style={tw`mb-10
            `}>
                <Heading style={tw`text-5xl`}>Филтри</Heading>
                <Text style={tw`text-sm mt-1`}>
                  Изберете категории и тагове, по които да филтрирате рецептите
                </Text>
                <View style={tw`h-[2px] mt-3 w-full bg-black bg-opacity-10`} />
              </View>

              {isLoading ? (
                <View style={tw`items-center justify-center`}>
                  <ActivityIndicator
                    size="large"
                    color={theme.extend.colors.primary}
                  />
                </View>
              ) : (
                <React.Fragment>
                  {/* <View style={tw`-mt-6`}>
                    <MultiSelectFilter
                      filterKey="accessibility"
                      label={'Достъпност'}
                      values={[]}
                      data={[]}
                    />
                  </View> */}

                  <View style={tw`-mt-6`}>
                    <MultiSelectFilter
                      filterKey="categories"
                      label={'Категории'}
                      values={categoryIds}
                      data={categories}
                    />
                  </View>

                  <MultiSelectFilter
                    filterKey="tags"
                    label={'Тагове'}
                    data={tags}
                    values={tagIds}
                  />
                </React.Fragment>
              )}
            </Container>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};
