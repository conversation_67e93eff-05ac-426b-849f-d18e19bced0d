import React from 'react';
import {
  RecipeFilterBadge,
  useRecipeFilterActions,
  FilterSection,
} from '@features/recipes';

export type MultipleSelectOnChange = {
  selectedId: number;
  operation: 'add' | 'remove';
};

type MultipleSelectProps = {
  valueExtractorKey?: string;
  label: string;
  filterKey: string;
  values: any[];
  data: {
    id: number;
    name: string;
    recipes_count?: number;
  }[];
};

export const MultiSelectFilter = ({
  label,
  filterKey,
  data,
  values = [],
  valueExtractorKey = 'id',
}: MultipleSelectProps) => {
  const actions = useRecipeFilterActions();

  const isSelected = React.useCallback(
    _val => {
      return values.includes(_val);
    },
    [values],
  );

  const onSelect = value => {
    if (isSelected(value)) {
      actions.removeFilter({ key: filterKey, value });
      return;
    }

    actions.addFilter({ key: filterKey, value });
  };

  return (
    <FilterSection label={label}>
      {data &&
        data?.map(item => (
          <RecipeFilterBadge
            key={item.id}
            label={
              item.name +
              ' ' +
              (filterKey === 'categories'
                ? '(' + item?.recipes_count + ')'
                : '')
            }
            onPress={() => onSelect(item[valueExtractorKey])}
            active={isSelected(item[valueExtractorKey])}
          />
        ))}
    </FilterSection>
  );
};
