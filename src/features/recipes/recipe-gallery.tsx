import React from 'react';
import { Dimensions, ScrollView, View } from 'react-native';

import RemoteImage from '@components/RemoteImage';
import tw from '../../utils/tailwind';

const { width } = Dimensions.get('window');

export const RecipeGallery = ({ images }) => {
  const [page, setPage] = React.useState<number>(0);

  const onScrollEnd = React.useCallback(e => {
    let contentOffset = e.nativeEvent.contentOffset;
    let viewSize = e.nativeEvent.layoutMeasurement;

    // Divide the horizontal offset by the width of the view to see which page is visible
    let pageNum = Math.floor(contentOffset.x / viewSize.width);
    setPage(pageNum);
  }, []);

  if (!images || images.length === 0) return null;

  return (
    <View style={tw`items-center`}>
      <ScrollView
        horizontal
        pagingEnabled
        style={tw`bg-white`}
        showsHorizontalScrollIndicator={false}
        bounces={false}
        contentContainerStyle={tw`bg-white h-100`}
        onMomentumScrollEnd={onScrollEnd}>
        {images?.map(image => (
          <RemoteImage
            key={image.uuid}
            uri={image?.original_url ?? ''}
            containerStyle={[tw`h-100`, { width }]}
            imageStyle={tw`aspect-4/5`}
          />
        ))}
      </ScrollView>
      <View
        style={tw`flex-row items-center justify-center absolute bottom-2 left-0 right-0`}>
        {images && images?.length > 1
          ? images?.map((_, idx) => <Dot active={idx === page} key={idx} />)
          : null}
      </View>
    </View>
  );
};

const Dot = React.memo(function ({ active }: { active: boolean }) {
  return (
    <View
      style={[
        tw`w-3 h-3 rounded-full mx-1`,
        tw.style(active ? 'bg-primary shadow-md' : 'bg-veganna-gray'),
      ]}
    />
  );
});
