import { create } from 'zustand';

interface RecipeFilterStore {
  shouldApplyFilters: boolean;
  isFilterModalOpen : boolean;
  orderBy           : 'latest' | 'popular' | undefined;
  searchQuery       : string;
  tags              : number[];
  categories        : number[];

  actions: {
    init            : () => void;
    openFilterModal : () => void;
    closeFilterModal: () => void;
    onSearchChange  : (searchQuery: string) => void;
    toggleOrderBy   : () => void;
    addFilter       : (payload: { key: string, value: number }) => void;
    removeFilter    : (payload: { key: string, value: number }) => void;
  };
}


export const useRecipeFilterStore = create<RecipeFilterStore>()(
  (set, get) => ({
    shouldApplyFilters: false,
    isFilterModalOpen : false,
    searchQuery       : "",
    tags              : [],
    categories        : [],
    orderBy           : undefined,

    actions: {
      init: () =>
        set({
          isFilterModalOpen: false,
          searchQuery      : "",
          tags             : [],
          categories       : [],
        }),
      openFilterModal : () => set({ isFilterModalOpen: true, shouldApplyFilters : false }),
      closeFilterModal: () => set({ isFilterModalOpen: false, shouldApplyFilters: true }),
      onSearchChange  : (searchQuery) => set({ searchQuery }),
      toggleOrderBy   : () => set(state => ({ orderBy: !state.orderBy || state.orderBy === 'latest' ? 'popular' : 'latest' })),
      addFilter: ({ key, value}) => {
        if(get()[key].includes(value)) return;
    
        set(draft => ({ [key]: [...draft[key], value ]}));
      },
      removeFilter: ({ key, value }) => set(draft => ({ [key]: draft[key].filter(v => v !== value) }))
    },
  }),
);

export const useRecipeFilterActions = () =>
  useRecipeFilterStore(state => state.actions);