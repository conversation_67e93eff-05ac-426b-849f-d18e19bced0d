import { useRoute } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';

import { useRecipeAccess } from '@utils/hooks';
import { getRecipeById } from '@utils/queries';

export const useSingleRecipe = () => {
  const route = useRoute<any>();

  const userHasAccess = useRecipeAccess({
    recipeIsPremium: route?.params?.premium,
  });

  const isQueryEnabled =
    route.params.id !== undefined &&
    ((route?.params?.premium && userHasAccess) || !route?.params?.premium);

  const query = useQuery(
    ['recipe', route.params.id],
    () => getRecipeById(route.params.id),
    {
      enabled: isQueryEnabled,
    },
  );

  return query;
};
