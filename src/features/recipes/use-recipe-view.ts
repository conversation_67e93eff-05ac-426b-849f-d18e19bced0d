import { updateRecipeViews } from '@utils/mutations';
import { useEffect, useRef } from 'react';

export const useRecipeView = (recipeId: number) => {
  const hasAppliedViewCount = useRef(false);

  const applyViews = async () => {
    await updateRecipeViews({ recipeId });
    hasAppliedViewCount.current = true;
  };

  useEffect(() => {
    if (hasAppliedViewCount.current || !recipeId) return;
    applyViews();
  }, [recipeId]);
};
