import { View, Pressable } from 'react-native';
import React from 'react';
import {
  useRecipeFilterActions,
  useRecipeFilterStore,
} from '../recipe-filter.store';
import { ArrowsUpDownIcon } from 'react-native-heroicons/outline';
import tailwindConfig from 'tailwind.config';
import tw from '@utils/tailwind';
import Text from '@features/ui/shared/Text';

export const OrderByToggle = () => {
  const actions = useRecipeFilterActions();
  const orderBy = useRecipeFilterStore(state => state.orderBy);
  const orderByLabel =
    !orderBy || orderBy === 'latest' ? 'най-нови' : 'най-популярни';

  return (
    <Pressable
      style={tw`flex-row py-1 gap-x-1`}
      onPress={actions.toggleOrderBy}>
      <ArrowsUpDownIcon
        size={20}
        color={tailwindConfig.theme.extend.colors.black}
      />
      <View>
        <Text style={tw`text-sm font-medium`}>
          Сортиране по <Text style={tw`text-sm font-bold`}>{orderByLabel}</Text>
        </Text>
      </View>
    </Pressable>
  );
};
