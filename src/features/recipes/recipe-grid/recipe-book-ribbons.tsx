import { View } from 'react-native';
import React from 'react';
import { Book } from '@utils/queries';
import { RecipeBookLabel } from './recipe-book-label';
import tw from '@utils/tailwind';

type Props = {
  books: Book[];
};

const RecipeBookRibbons = (props: Props) => {
  return (
    <View style={tw`absolute top-0 left-0 right-0`}>
      {props.books.map(book => (
        <RecipeBookLabel key={book.id} bookTitle={book.short_title} />
      ))}
    </View>
  );
};

export default RecipeBookRibbons;
