import { TextInput } from 'react-native';
import React from 'react';
import { useRecipeFilterActions, useRecipeFilterStore } from '../recipe-filter.store';
import { theme } from 'tailwind.config';
import tw from '@utils/tailwind';

export const SearchInput = () => {
  const actions         = useRecipeFilterActions();
  const searchQuery     = useRecipeFilterStore(state => state.searchQuery);

  return (
    <TextInput
      value={searchQuery}
      onChangeText={actions.onSearchChange}
      style={tw`h-13 flex-grow bg-gray-100 rounded-lg px-4`}
      placeholder={'Търси рецепта или съставка'}
      clearButtonMode="while-editing"
      placeholderTextColor={theme.extend.colors.black}
      autoCapitalize="none"
      returnKeyType="search"
      returnKeyLabel="Търси"
    />
  );
};
