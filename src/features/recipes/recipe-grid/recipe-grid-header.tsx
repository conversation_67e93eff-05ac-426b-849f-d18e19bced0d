import React from 'react';
import { View } from 'react-native';
import {
  FiltersToggle,
  OrderByToggle,
  SearchInput,
} from '@features/recipes';
import tw from '@utils/tailwind';

export const RecipeGridHeader = () => {
  return (
    <View style={tw`border-b border-gray-100`}>
      <View style={tw`flex-row items-center px-4 overflow-hidden pt-2 pb-2'`}>
        <SearchInput />
        <FiltersToggle />
      </View>

      <View style={tw`px-4 self-start py-1`}>
        <OrderByToggle />
      </View>
    </View>
  );
};
