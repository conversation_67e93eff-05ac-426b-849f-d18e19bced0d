import React, { useCallback } from 'react';
import { Pressable, Text, View } from 'react-native';
import Reanimated, {
  Layout,
  SlideInDown,
  SlideOutDown,
} from 'react-native-reanimated';

import { useScreenSize } from '@utils/hooks';
import { useDataStore } from '@utils/store';
import tw from '@utils/tailwind';
import { useInfiniteRecipes } from '../use-infinite-recipes';

type Props = {
  showFreeRecipesOnly: boolean;
  onShowFreeRecipes: () => void;
  onShowAllRecipes: () => void;
};

/**
 * TEMP: The current filtering is a temporary solution until the api is configured.
 */
export const CallToActionBar: React.FC<Props> = ({
  showFreeRecipesOnly,
  onShowFreeRecipes,
  onShowAllRecipes,
}) => {
  const { isSmallScreen } = useScreenSize();

  const query = useInfiniteRecipes();
  const isUserPremium = useDataStore(state => state.isUserPremium);
  const showPremiumModal = useDataStore(
    state => state.setShouldShowPremiumModal,
  );

  const handleFreeRecipesPress = useCallback(() => {
    if (showFreeRecipesOnly) {
      onShowAllRecipes();
    } else {
      onShowFreeRecipes();
    }
  }, [showFreeRecipesOnly, onShowFreeRecipes, onShowAllRecipes]);

  const handleActionPress = useCallback(() => {
    showPremiumModal(true);
  }, [showPremiumModal]);

  if (isUserPremium || query.isLoading || query.isError) {
    return null;
  }

  return (
    <Reanimated.View
      entering={SlideInDown.springify().damping(20).stiffness(90)}
      exiting={SlideOutDown.springify()}
      layout={Layout.springify()}
      style={tw`absolute bottom-0 left-0 right-0 z-50`}>
      <View
        style={[
          tw`absolute inset-0 bg-gray-200/50 rounded-t-[34px]`,
          {
            transform: [{ translateY: -1 }],
          },
        ]}
      />
      <View
        style={[
          tw`bg-white rounded-t-[32px] px-6 py-3 shadow-lg shadow-black/15`,
          {
            shadowOffset: { width: 0, height: -2 },
            elevation: 24,
          },
        ]}>
        <View style={tw`flex-row items-center justify-between`}>
          <View style={tw`flex-1 mr-3`}>
            <Text
              style={tw`${
                isSmallScreen ? 'text-[13px]' : 'text-[14px]'
              } text-gray-800 leading-[18px] font-body-regular`}>
              {`Отключете цялото съдържание или ${
                showFreeRecipesOnly ? 'разгледайте' : 'опитайте'
              } `}

              <Pressable
                onPress={handleFreeRecipesPress}
                style={({ pressed }) => [
                  tw`${pressed ? 'opacity-50' : 'opacity-100'}`,
                ]}
                android_ripple={{ color: 'transparent' }}>
                <Text
                  style={tw`${
                    isSmallScreen ? 'text-[13px]' : 'text-[14px]'
                  } text-primary font-body-medium underline`}>
                  {showFreeRecipesOnly
                    ? 'всички рецепти'
                    : 'безплатните рецепти'}
                </Text>
              </Pressable>
            </Text>
          </View>

          <Pressable
            onPress={handleActionPress}
            style={tw`bg-primary px-5 py-2.5 rounded-full shadow-md shadow-primary/25 self-center`}>
            <Text style={tw`text-white font-body-semibold text-[14px]`}>
              Отключи
            </Text>
          </Pressable>
        </View>
      </View>
    </Reanimated.View>
  );
};
