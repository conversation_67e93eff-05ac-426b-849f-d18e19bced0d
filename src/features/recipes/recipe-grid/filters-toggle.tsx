import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { FunnelIcon } from 'react-native-heroicons/outline';

import tw from '@utils/tailwind';
import { theme } from 'tailwind.config';
import {
  useRecipeFilterActions,
  useRecipeFilterStore,
} from '../recipe-filter.store';

export const FiltersToggle = () => {
  const actions = useRecipeFilterActions();
  const categories = useRecipeFilterStore(state => state.categories.length);
  const tags = useRecipeFilterStore(state => state.tags.length);

  const filterCount = categories + tags;

  return (
    <TouchableOpacity
      onPress={actions.openFilterModal}
      activeOpacity={0.7}
      style={tw`bg-background w-13 ml-4 h-13 flex-shrink rounded-xl items-center justify-center`}>
      {filterCount > 0 ? (
        <View
          style={tw`absolute items-center justify-center shadow -right-1 -top-2 bg-red-300 w-5.5 h-5.5 rounded-full`}>
          <Text style={tw`font-body-bold text-[13px]`}>
            {filterCount > 50 ? '50+' : filterCount}
          </Text>
        </View>
      ) : null}
      <FunnelIcon color={theme.extend.colors.black} size={27} />
    </TouchableOpacity>
  );
};
