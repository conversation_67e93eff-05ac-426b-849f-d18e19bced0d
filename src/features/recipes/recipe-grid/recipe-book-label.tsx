import React from 'react';
import { Image, ImageBackground, Platform, View } from 'react-native';

import Heading from '@features/ui/shared/Heading';
import tw from '@utils/tailwind';

type Props = {
  bookTitle?: string | null;
};

export const RecipeBookLabel = (props: Props) => {
  if (!props.bookTitle) return null;

  const fontSizeStyle = Platform.select({
    ios: tw`text-[20px]`,
    android: tw`text-sm`,
  });

  return (
    <View style={tw`mt-auto pb-1`}>
      <ImageBackground
        style={tw`h-13 max-w-36 items-start justify-center`}
        source={require('../../../assets/images/book_placeholder.png')}>
        <View style={tw`mx-4 mt-3 pl-6 flex-row items-center justify-between`}>
          <Heading
            style={[
              tw`text-background flex-1 mb-2`,
              fontSizeStyle,
              { fontWeight: 'bold' },
            ]}>
            {props.bookTitle}
          </Heading>

          <View style={tw`mb-1.5`}>
            <Image
              source={require('../../../assets/images/book_icon.png')}
              style={tw`-mr-2 w-10 h-12`}
            />
          </View>
        </View>
      </ImageBackground>
    </View>
  );
};
