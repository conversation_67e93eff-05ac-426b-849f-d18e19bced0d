import React from 'react';
import { View } from 'react-native';
import tw from '../../utils/tailwind';
import Container from '../ui/shared/Container';
import Heading from '../ui/shared/Heading';
import { RecipeMacro } from '@features/recipes';
import Text from '../ui/shared/Text';

const RecipeNutritionInfoComponent = ({ calories, protein, carbs, fat }) => {
  return (
    <View style={tw`bg-background/50 py-6`}>
      <Container>
        <View style={tw`flex-row items-center justify-between`}>
          <View style={tw`flex-shrink`}>
            <Heading style={tw`text-3xl font-heading-bold`}>
              Хранителна стойност
            </Heading>
            <Text style={tw`text-sm font-body-bold`}>за 1 порция</Text>
          </View>
          <Text style={tw`text-sm font-body-bold ml-10`}>
            {calories} калории
          </Text>
        </View>

        <View style={tw`mt-6 flex-row pb-4 justify-between`}>
          <RecipeMacro label={protein} macro={'протеин'} />
          <RecipeMacro label={carbs} macro={'въглехидр.'} />
          <RecipeMacro label={fat} macro={'мазнини'} />
        </View>
      </Container>
    </View>
  );
};

export const RecipeNutritionInfo = React.memo(RecipeNutritionInfoComponent);
