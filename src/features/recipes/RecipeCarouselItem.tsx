import React from 'react';
import { View } from 'react-native';
import { LockClosedIcon } from 'react-native-heroicons/solid';

import RemoteImage from '@components/RemoteImage';
import { AddToFavouritesButton } from '@features/ui/button';
import PremiumHoc from '../../components/PremiumHoc';
import { FALLBACK_IMAGE_URI } from '../../utils/api';
import { useDataStore } from '../../utils/store';
import tw from '../../utils/tailwind';
import Text from '../ui/shared/Text';
import RecipeBookRibbons from './recipe-grid/recipe-book-ribbons';
import { Recipe } from './types';

const RecipeCarouselItem = ({
  large = false,
  isBookLabelVisible = true,
  ...props
}: Recipe & { large?: boolean; isBookLabelVisible?: boolean }) => {
  const { id, title, image, premium: recipeIsPremium, books } = props;

  const isUserPremium = useDataStore(state => state.isUserPremium);
  const showImageOverlay = recipeIsPremium && !isUserPremium;

  const [uri, setUri] = React.useState(image);

  const renderBookRibbons = () => {
    if (!books || !isBookLabelVisible) return null;

    return <RecipeBookRibbons books={books} />;
  };

  const loadFallback = () => {
    setUri(FALLBACK_IMAGE_URI);
  };

  return (
    <View
      style={[
        tw`mr-6 bg-white shadow-md rounded-b-lg`,
        tw.style(large ? 'h-[400px]' : 'h-[300px]'),
        tw.style(large ? 'w-[320px]' : 'w-[300px]'),
      ]}>
      {/* Container for Image */}
      <RemoteImage
        uri={uri || FALLBACK_IMAGE_URI}
        containerStyle={[
          tw.style(large ? 'h-[320px]' : 'h-[220px]'),
          tw.style(large ? 'w-[320px]' : 'w-[300px]'),
        ]}
        imageStyle={tw`rounded-t-lg`}
        handleError={loadFallback}>
        {/* Book Ribbons */}
        {renderBookRibbons()}

        {/* Image Overlay */}
        {showImageOverlay ? (
          <View
            style={tw`w-full h-full bg-slate-500 bg-opacity-50 rounded-t-lg`}>
            <LockClosedIcon
              style={tw`ml-auto mr-5 mt-5`}
              size={25}
              stroke={'#fff'}
              fill={'#fff'}
            />
          </View>
        ) : null}
      </RemoteImage>

      {/* Favourites Button */}
      <View style={tw`absolute right-3 bottom-[95px]`}>
        <AddToFavouritesButton
          premium={recipeIsPremium}
          recipeTitle={title}
          recipeId={id}
        />
      </View>

      {/* Text Container */}
      <View
        style={tw`p-4 bg-white rounded-b-lg flex-row justify-between items-center h-[80px]`}>
        <Text
          numberOfLines={2}
          style={tw`text-[15px] font-body-bold text-black leading-snug self-start flex-shrink`}>
          {title}
        </Text>
      </View>
    </View>
  );
};

export default React.memo(PremiumHoc(RecipeCarouselItem));
