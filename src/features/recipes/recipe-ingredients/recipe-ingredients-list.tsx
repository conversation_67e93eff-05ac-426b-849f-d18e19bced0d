import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import tw from '../../../utils/tailwind';
import { getIngredientUnit, getIngredientValue } from './utils';
import Heading from '../../ui/shared/Heading';
import { RecipeIngredient } from './recipe-ingredient';
import Text from '../../ui/shared/Text';
import { useShoppingListActions } from '@features/shopping-list/shopping-list.store';

export const RecipeIngredientsList = ({
  ingredients,
  portionCount,
  recipeTitle,
  recipePortionSize,
}) => {
  const actions = useShoppingListActions();
  const [selectedIngredients, setSelectedIngredients] = React.useState<
    string[]
  >(ingredients?.flatMap(item => item.ingredients).map(ing => ing.uuid) || []);

  const toggleSelection = React.useCallback(
    (uuid: string) => {
      if (!selectedIngredients.includes(uuid)) {
        setSelectedIngredients([...selectedIngredients, uuid]);
        return;
      }

      setSelectedIngredients(
        selectedIngredients.filter(ingUuid => ingUuid !== uuid),
      );
    },
    [setSelectedIngredients, selectedIngredients],
  );

  const isAddBtnDisabled = selectedIngredients.length === 0;

  React.useEffect(() => {
    if (ingredients) {
      setSelectedIngredients(
        ingredients.flatMap(item => item.ingredients).map(ing => ing.uuid),
      );
    }
  }, [ingredients]);

  const generateLabels = React.useCallback(
    (
      s,
      {
        name,
        is_optional,
        value_metric,
        value_imperial,
        numerator,
        denominator,
        unit_imperial,
        unit_imperial_plural,
        unit_metric,
        unit_metric_plural,
        metric_numerator,
        metric_denominator,
      },
    ) => {
      let value = s === 'metric' ? value_metric : value_imperial;

      const ingValue = getIngredientValue(s, {
        value,
        numerator,
        denominator,
        portionCount,
        metric_numerator,
        metric_denominator,
      });

      const ingUnit = getIngredientUnit(s, {
        value,
        portionCount,
        unit_imperial,
        unit_imperial_plural,
        unit_metric,
        unit_metric_plural,
        numerator,
        denominator,
        metric_numerator,
        metric_denominator,
      });

      const theValue = () => {
        if (ingValue && Array.isArray(ingValue)) {
          const [whole, fr] = ingValue;

          return whole && typeof whole !== undefined
            ? whole + ' '
            : '' + fr && typeof fr !== undefined
            ? fr + ' '
            : '';
        }

        return ingValue;
      };

      const label =
        (theValue() || '') +
        (ingUnit ?? '') +
        name +
        (is_optional ? ' (по избор)' : '');

      return label;
    },
    [portionCount],
  );

  const toShoppingList = () => {
    const ings = ingredients
      .flatMap(ingGroup => ingGroup.ingredients)
      .filter(ing => selectedIngredients.includes(ing.uuid))
      .map(ing => {
        const ingredient = {
          key: ing.uuid,
          metric: generateLabels('metric', { ...ing }),
          imperial: generateLabels('imperial', { ...ing }),
        };
        return ingredient;
      });

    const portions = portionCount * recipePortionSize;
    const portionsLabel = portions > 1 ? ' порции' : ' порция';

    const key = recipeTitle + ', ' + portions + portionsLabel;

    actions.addToList(key, ings);
  };

  return (
    <React.Fragment>
      <View style={tw`mt-4`}>
        {ingredients
          ? ingredients.map((ingredientGroup, idx: number) => (
              <View key={ingredientGroup.uuid || idx} style={tw`mb-4`}>
                {ingredientGroup?.heading ? (
                  <Heading>{ingredientGroup?.heading}</Heading>
                ) : null}
                {ingredientGroup?.ingredients?.map(ing => (
                  <RecipeIngredient
                    key={ing.uuid}
                    {...ing}
                    portionCount={portionCount}
                    onSelect={toggleSelection}
                    selected={selectedIngredients.includes(ing.uuid)}
                  />
                ))}
              </View>
            ))
          : null}
      </View>
      <TouchableOpacity
        onPress={toShoppingList}
        style={[tw`mt-6`, tw.style(isAddBtnDisabled && 'opacity-40')]}
        disabled={isAddBtnDisabled}>
        <View
          style={tw`flex-row items-center justify-center bg-primary rounded-lg p-3`}>
          <Text
            style={tw`text-center font-body-bold text-white text-[14px] leading-snug`}>
            {isAddBtnDisabled
              ? 'Изберете съставки за добавяне'
              : `Добавяне на ${selectedIngredients.length} ${
                  selectedIngredients.length > 1 ? 'съставки' : 'съставка'
                }`}
            {'\n'}в списъка за пазаруване
          </Text>
        </View>
      </TouchableOpacity>
    </React.Fragment>
  );
};
