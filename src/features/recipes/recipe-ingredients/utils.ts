import Fraction from 'fraction.js';

export function getIngredientValue(
  system,
  {
    value,
    numerator,
    metric_numerator,
    denominator,
    metric_denominator,
    portionCount,
  },
) {
  if (!value && system === 'imperial' && (!numerator || !denominator)) {
    return null;
  }

  if (
    !value &&
    system === 'metric' &&
    (!metric_numerator || !metric_denominator)
  ) {
    return null;
  }

  if (value !== null && system === 'imperial' && numerator && denominator) {
    const x = new Fraction(`${value} ${numerator}/${denominator}`);
    return x.mul(portionCount).toFraction(true).split(' ');
  } else if (
    value !== null &&
    system === 'metric' &&
    metric_numerator &&
    metric_denominator
  ) {
    const x = new Fraction(
      `${value} ${metric_numerator}/${metric_denominator}`,
    );
    return x.mul(portionCount).toFraction(true).split(' ');
  }

  return value * portionCount + ' ';
}

export function getIngredientUnit(
  system,
  {
    value,
    portionCount,
    unit_imperial,
    unit_imperial_plural,
    unit_metric,
    unit_metric_plural,
    metric_numerator,
    metric_denominator,
    numerator,
    denominator,
  },
) {
  function transform() {
    if (system === 'metric') {
      if (
        (value === 0 || value === null) &&
        !!metric_numerator &&
        !!metric_denominator
      ) {
        const res = new Fraction(`${metric_numerator}/${metric_denominator}`)
          .mul(portionCount)
          .compare(1);

        if (res > 0) {
          return unit_metric_plural;
        }

        return unit_metric;
      } else if (value > 0 && !!metric_numerator && !!metric_denominator) {
        const res = new Fraction(
          `${value} ${metric_numerator}/${metric_denominator}`,
        )
          .mul(portionCount)
          .compare(1);

        if (res > 0) {
          return unit_metric_plural;
        }

        return unit_metric;
      } else {
        return value && value * portionCount > 1
          ? unit_metric_plural
          : unit_metric;
      }
    } else if (system === 'imperial') {
      if ((value === 0 || value === null) && !!numerator && !!denominator) {
        const res = new Fraction(`${numerator}/${denominator}`)
          .mul(portionCount)
          .compare(1);

        if (res > 0) {
          return unit_imperial_plural;
        }
        return unit_imperial;
      } else if (value > 0 && !!numerator && !!denominator) {
        const res = new Fraction(`${value} ${numerator}/${denominator}`)
          .mul(portionCount)
          .compare(1);

        if (res > 0) {
          return unit_imperial_plural;
        }

        return unit_imperial;
      } else {
        return value && value * portionCount > 1
          ? unit_imperial_plural
          : unit_imperial;
      }
    }
  }

  const unit = transform();

  if (!unit) {
    return '';
  }

  return unit + ' ';
}
