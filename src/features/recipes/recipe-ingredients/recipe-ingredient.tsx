import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { MeasurementSystem, useDataStore } from '../../../utils/store';
import tw from '../../../utils/tailwind';
import { getIngredientUnit, getIngredientValue } from './utils';
import CheckBox from '../../../components/Checkbox';
import Text from '../../ui/shared/Text';

const RecipeIngredientComponent = ({ ...props }) => {
  const system = useDataStore<MeasurementSystem>(state => state.system);
  const {
    name,
    numerator,
    metric_numerator,
    denominator,
    metric_denominator,
    value_metric,
    value_imperial,
    unit_metric,
    unit_metric_plural,
    unit_imperial,
    unit_imperial_plural,
    is_optional,
    portionCount,
    onSelect,
    uuid,
    selected,
  } = props;

  const value = React.useMemo(
    () => (system === 'metric' ? value_metric : value_imperial),
    [value_metric, value_imperial, system],
  );

  const unit = React.useMemo(() => {
    return getIngredientUnit(system, {
      value,
      metric_numerator,
      metric_denominator,
      portionCount,
      unit_imperial,
      unit_imperial_plural,
      unit_metric,
      unit_metric_plural,
      numerator,
      denominator,
    });
  }, [
    system,
    unit_metric,
    unit_imperial,
    metric_numerator,
    metric_denominator,
    value,
    unit_metric_plural,
    unit_imperial_plural,
    portionCount,
    numerator,
    denominator,
  ]);

  const ingValue = React.useMemo(() => {
    return getIngredientValue(system, {
      value,
      numerator,
      metric_numerator,
      metric_denominator,
      denominator,
      portionCount,
    });
  }, [
    system,
    value,
    numerator,
    denominator,
    portionCount,
    metric_numerator,
    metric_denominator,
  ]);

  const renderIngredientValue = React.useCallback(() => {
    if (ingValue && typeof ingValue === 'object') {
      const [whole, fr] = ingValue;
      return (
        <Text>
          {whole}{' '}
          {fr ? (
            <Text style={tw`text-[12px] font-body-bold`}>{fr} </Text>
          ) : null}
        </Text>
      );
    }

    return ingValue;
  }, [ingValue]);

  const handlePress = React.useCallback(() => {
    onSelect(uuid);
  }, [onSelect, uuid]);

  return (
    <TouchableOpacity onPress={handlePress} style={tw`my-1`}>
      <View style={tw`flex-row items-center`}>
        <CheckBox selected={selected} />
        <Text style={tw`ml-2 text-[15px] flex-shrink`}>
          {renderIngredientValue()}
          {unit}
          {name}
          {is_optional ? (
            <Text style={tw`text-black/50 text-sm`}> (по избор)</Text>
          ) : null}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

export const RecipeIngredient = React.memo(RecipeIngredientComponent);
