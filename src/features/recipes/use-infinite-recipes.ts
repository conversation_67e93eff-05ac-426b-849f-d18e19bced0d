import { useInfiniteQuery } from '@tanstack/react-query';
import { axiosInstance } from '@utils/api';
import { useRecipeFilterStore } from './recipe-filter.store';
import { useDebounce } from '@utils/hooks';
import { RecipeResponsePage } from './types';

export const useInfiniteRecipes = () => {
  const searchQuery = useRecipeFilterStore(state => state.searchQuery);
  const debouncedSearch = useDebounce(searchQuery, 500);

  const categories = useRecipeFilterStore(state => state.categories);
  const tags = useRecipeFilterStore(state => state.tags);
  const orderBy = useRecipeFilterStore(state => state.orderBy);

  const showFilters = useRecipeFilterStore(state => state.isFilterModalOpen);

  const shouldApplyFilters = useRecipeFilterStore(
    state => state.shouldApplyFilters,
  );

  const query = useInfiniteQuery(
    ['all-recipes', debouncedSearch, categories, tags, orderBy],
    loadRecipes,
    {
      getNextPageParam,
      retry: false,
      enabled: !showFilters || shouldApplyFilters,
    },
  );

  return query;
};

export const useInfiniteBookRecipes = (bookId: number) => {
  const query = useInfiniteQuery(['book-recipes', bookId], getRecipesByBook, {
    getNextPageParam,
    retry: false,
  });

  return query;
};

export async function getRecipesByBook({ pageParam = 1, queryKey }) {
  const [, bookId] = queryKey;

  const params = new URLSearchParams();
  params.append('page', pageParam.toString());

  return (
    await axiosInstance.get(`/books/${bookId}/recipes?${params.toString()}`)
  ).data;
}

const loadRecipes = async ({ pageParam = 1, queryKey }) => {
  const [, searchTerm, categoryIds, selectedTagsIds, order] = queryKey;

  const params = new URLSearchParams();
  params.append('page', pageParam.toString());

  if (searchTerm) {
    params.append('search', searchTerm);
  }

  const categoryIdsParam = categoryIds.join(',');
  const selectedTagIdsParam = selectedTagsIds.join(',');

  if (useRecipeFilterStore.getState().shouldApplyFilters) {
    if (categoryIdsParam) {
      params.append('categoryIds', categoryIdsParam);
    }

    if (selectedTagIdsParam) {
      params.append('tagIds', selectedTagIdsParam);
    }
  }

  if (order) {
    params.append('order', order);
  }

  return (await axiosInstance.get(`/recipes?${params.toString()}`)).data;
};

export const transformInfinitelyPaginatedRecipes = (
  pages: RecipeResponsePage[],
) => {
  return pages?.map(page => page?.data).flat();
};

const getNextPageParam = (lastPage: RecipeResponsePage) => {
  const hasNextPage = lastPage.meta.current_page < lastPage.meta.last_page;
  return hasNextPage ? lastPage.meta.current_page + 1 : undefined;
};
