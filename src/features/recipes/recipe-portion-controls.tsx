import React from 'react';
import { Pressable, View } from 'react-native';
import { MinusIcon, PlusIcon } from 'react-native-heroicons/outline';
import tw from '../../utils/tailwind';

export const RecipePortionControls = ({ count = 1, setCount }) => {
  const handleChange = React.useCallback(
    (operation: 'add' | 'subtract') => {
      if (operation === 'add') {
        setCount(prevCount => prevCount + 1);
      } else if (operation === 'subtract' && count >= 1) {
        setCount(prevCount => prevCount - 1);
      }
    },
    [count, setCount],
  );
  return (
    <View
      style={tw`flex-row flex-shrink items-center justify-center rounded-lg`}>
      <Pressable
        onPress={() => handleChange('subtract')}
        disabled={count <= 1}
        style={({ pressed }) => [
          tw`py-2 px-3 rounded-l-lg bg-primary z-10`,
          tw.style(pressed && 'opacity-80'),
          tw.style(count <= 1 && 'opacity-70'),
        ]}>
        <MinusIcon color="#fff" />
      </Pressable>
      <View
        style={tw`h-[50%] rounded self-center w-[1px] -mx-[1px] z-20 bg-black/15`}
      />
      <Pressable
        onPress={() => handleChange('add')}
        style={({ pressed }) => [
          tw`py-2 px-3 rounded-r-lg bg-primary z-10`,
          tw.style(pressed && 'opacity-80'),
        ]}>
        <PlusIcon color="#fff" />
      </Pressable>
    </View>
  );
};
