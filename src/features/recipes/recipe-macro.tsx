import React from 'react';
import { View } from 'react-native';
import tw from '../../utils/tailwind';
import Text from '../ui/shared/Text';

export const RecipeMacro = ({ label, macro }) => {
  return (
    <View
      style={tw`w-[30%] bg-primary rounded-lg px-2 py-2 items-center justify-center`}>
      <View style={tw`items-center`}>
        <Text style={tw`text-white text-center font-body-bold`}>
          {label} гр.
        </Text>
        <Text style={tw`text-[13px] text-white font-body-semibold -mt-1`}>
          {macro}
        </Text>
      </View>
    </View>
  );
};
