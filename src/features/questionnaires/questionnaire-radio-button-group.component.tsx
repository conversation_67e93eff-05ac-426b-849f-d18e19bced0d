import React from 'react';
import { Controller, UseControllerProps } from 'react-hook-form';
import { TouchableOpacity, View } from 'react-native';
import { InputError } from '@features/ui/shared';
import { CheckIcon } from 'react-native-heroicons/outline';
import tw from '@utils/tailwind';
import { theme } from 'tailwind.config';
import Text from '@features/ui/shared/Text';

type Props = {
  options: { value: string }[];
} & UseControllerProps;

export const QuestionnaireRadioButtonGroup = ({
  name,
  rules,
  options,
}: Props) => {
  return (
    <Controller
      name={name}
      rules={rules}
      render={({ field, fieldState }) => (
        <View style={tw`gap-y-2`}>
          {options.map((option, idx) => (
            <TouchableOpacity
              key={option.value + idx}
              onPress={() => field.onChange(option.value)}
              style={tw`rounded-md p-2 border border-primary flex-row gap-x-2 items-center pr-10`}>
              <Text style={tw`text-sm flex-shrink`}>{option.value}</Text>
              {option.value === field.value ? (
                <View
                  style={tw`absolute right-2 top-0 bottom-0 items-center justify-center`}>
                  <View style={tw`w-6 h-6 items-center justify-center rounded-full bg-primary`}>
                    <CheckIcon
                      size={18}
                      color={theme.extend.colors.background}
                      strokeWidth={2}
                    />
                  </View>
                </View>
              ) : null}
            </TouchableOpacity>
          ))}

          {fieldState?.error ? (
            <InputError>{fieldState.error.message}</InputError>
          ) : null}
        </View>
      )}></Controller>
  );
};
