import { View, Text } from 'react-native';
import React, { useEffect, useState } from 'react';
import { convertTime } from '../../utils/datetime';
import { useQuestionnaireActions } from '@features/questionnaires';
import tw from '@utils/tailwind';

export const QuestionnaireCountdown = ({ timeout }: { timeout: number }) => {
  const { endQuestionnaire } = useQuestionnaireActions();
  const [secondsLeft, setSecondsLeft] = useState(timeout);
  const timeIsUp = secondsLeft <= 0;

  useEffect(() => {
    if (!timeIsUp) return;

    endQuestionnaire();
  }, [timeIsUp]);

  useEffect(() => {
    if (timeIsUp) return;

    const handle = setInterval(
      () => setSecondsLeft(seconds => seconds - 1),
      1000,
    );

    return () => clearInterval(handle);
  }, [secondsLeft]);

  return (
    <View
      style={tw`absolute top-0 right-0 left-0 justify-center p-2 z-99 bg-background w-full`}>
      <Text style={tw`text-black text-sm font-medium text-center`}>
        остават ви {convertTime(secondsLeft)}
      </Text>
    </View>
  );
};
