import TextInput from '@features/ui/shared/TextInput';
import tw from '@utils/tailwind';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { View } from 'react-native';
import Heading from '../ui/shared/Heading';
import Text from '../ui/shared/Text';
import {
  QuestionnaireRadioButtonGroup,
  BookQuestion,
} from '@features/questionnaires';

export const QuestionnaireQuestion = (props: BookQuestion) => {
  const { control } = useFormContext();

  const fieldName = `question_${props.id.toString()}`;

  return (
    <View>
      <Heading>{props.title}</Heading>
      {props?.helper_text ? (
        <Text style={tw`text-gray-700 text-black text-sm`}>
          {props.helper_text}
        </Text>
      ) : null}
      <View style={tw`py-2`}>
        {props.type === 'open' ? (
          <TextInput
            name={fieldName}
            control={control}
            rules={{ required: 'Моля въведете отговор' }}
          />
        ) : null}

        {props.type === 'single_choice' ? (
          <QuestionnaireRadioButtonGroup
            name={fieldName}
            rules={{ required: 'Моля изберете отговор' }}
            options={props?.options}
          />
        ) : null}
      </View>
    </View>
  );
};
