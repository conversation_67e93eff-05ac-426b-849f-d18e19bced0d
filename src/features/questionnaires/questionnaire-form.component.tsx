import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import {
  QuestionnaireQuestion,
  useQuestionnaireActions,
} from '@features/questionnaires';
import { getQuestionsByBook } from '../../utils/queries';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ActionButton } from '@features/ui/button';
import tw from '@utils/tailwind';
import { validateQuestionnaireAnswers } from '@utils/mutations';
import { FormProvider, useForm } from 'react-hook-form';
import ErrorMessage from '@features/ui/shared/ErrorMessage';
import Text from '@features/ui/shared/Text';

export const QuestionnaireForm = ({ bookId }: { bookId: number }) => {
  const queryClient = useQueryClient();

  const methods = useForm({
    defaultValues: {
      question_1: 'Анна',
      question_2: '55',
      question_3: 'тофу',
      question_4: 'тест',
      question_5: '123',
    },
  });
  const { finishQuestionnaire } = useQuestionnaireActions();

  const validateAnswers = useMutation({
    mutationFn: validateQuestionnaireAnswers,
  });

  const { data: questions, isLoading } = useQuery({
    queryKey: ['book-questions', bookId],
    queryFn: getQuestionsByBook,
    cacheTime: 0,
  });

  const onSubmit = async data => {
    const questions = Object.keys(data).map<{
      question_id: number;
      answer: string;
    }>(key => ({
      question_id: parseInt(key.split('_')[1]),
      answer: data[key],
    }));

    const result = await validateAnswers.mutateAsync({ bookId, questions });

    if (!result.data.success) return;

    await queryClient.invalidateQueries({
      queryKey: ['me'],
    });

    finishQuestionnaire();
  };

  if (isLoading) return <ActivityIndicator />;

  const errorMessage =
    validateAnswers.data?.data.message || validateAnswers.error;

  return (
    <View>
      <FormProvider {...methods}>
        <View style={tw`gap-y-8 mt-4`}>
          {questions?.map(question => (
            <QuestionnaireQuestion key={question.id} {...question} />
          ))}
        </View>
      </FormProvider>

      {errorMessage ? (
        <View style={tw`bg-white p-2 rounded-md items-center`}>
          <Text style={tw`text-red-500 text-center text-sm`}>
            {errorMessage}
          </Text>
        </View>
      ) : null}

      <ActionButton
        label="Завърши въпросника"
        loading={methods.formState.isLoading || validateAnswers.isLoading}
        onPress={methods.handleSubmit(onSubmit)}
        style={tw`mt-6`}
      />
    </View>
  );
};
