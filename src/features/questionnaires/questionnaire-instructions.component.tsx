import { View } from 'react-native';
import React from 'react';
import Heading from '../ui/shared/Heading';
import Text from '../ui/shared/Text';
import tw from '../../utils/tailwind';
import { ActionButton } from '@features/ui/button';
import { convertTime } from '../../utils/datetime';
import TimeIcon from '../../assets/svg/TimeIcon';
import { useQuestionnaireActions } from '@features/questionnaires';

export const QuestionnaireInstructions = ({ timeout }: { timeout: number }) => {
  const timeoutLabel = convertTime(timeout);

  const { startQuestionnaire } = useQuestionnaireActions();

  return (
    <>
      <Heading style={tw`text-4xl mb-1`}>Инструкции</Heading>

      <Text style={tw`mb-4 font-semibold`}>
        Как да получите достъп до всички рецепти от тази книга в приложението?
      </Text>

      <Text style={tw`font-semibold mb-1`}>
        Следвайте няколко лесни стъпки:
      </Text>

      <View style={tw`gap-y-2 mt-2 mb-10`}>
        <Text>1. Пригответе книгата близо до себе си.</Text>
        <Text>2. Отговорете на въпросите.</Text>
        <Text>3. Рецептите са отключени за вас, насладете им се 🤩</Text>
      </View>

      <View
        style={tw`bg-background rounded-lg items-center justify-center flex-row p-3 mb-6 gap-x-2`}>
        <TimeIcon style={tw`w-6 h-6 text-black/70`} />
        <Text style={tw`text-sm font-semibold`}>
          Разполагате с {timeoutLabel}
        </Text>
      </View>

      <ActionButton label="Към въпросника" onPress={startQuestionnaire} />
    </>
  );
};
