import { create } from 'zustand';

export type BookQuestion = {
  id: number;
  title: string;
  type: 'open' | 'single_choice';
  helper_text?: string;
  options: { value: string }[];
  answer: string;
};

interface QuestionnaireStore {
  isRunning: boolean;
  isSuccessful: boolean;
  isEnded: boolean;

  actions: {
    initQuestionnaire: () => void;
    startQuestionnaire: () => void;
    finishQuestionnaire: () => void;
    endQuestionnaire: () => void;
  };
}

export const useQuestionnaireStore = create<QuestionnaireStore>()(
  (set, get) => ({
    isSuccessful: false,
    isRunning: false,
    isEnded: false,

    actions: {
      initQuestionnaire: () =>
        set({
          isSuccessful: false,
          isRunning: false,
          isEnded: false,
        }),
      startQuestionnaire: () => set({ isRunning: true }),
      finishQuestionnaire: () => set({ isRunning: false, isSuccessful: true }),
      endQuestionnaire: () => set({ isRunning: false, isEnded: true }),
    },
  }),
);

export const useQuestionnaireActions = () =>
  useQuestionnaireStore(state => state.actions);
