import React from 'react';
import { Alert, TouchableOpacity } from 'react-native';
import { TrashIcon } from 'react-native-heroicons/outline';
import { theme } from '../../../tailwind.config';
import tw from '../../utils/tailwind';
import {
  useShoppingListActions,
  useShoppingListStore,
} from './shopping-list.store';

const DeleteItemsFromShoppingList = () => {
  const actions = useShoppingListActions();

  const hasItemsInShoppingList = useShoppingListStore(
    state => Object.keys(state.list).length > 0,
  );

  const handleDeletion = () => {
    Alert.alert(
      'Изберете какво искате да изтриете?',
      'Може да изтриете целия списък или само отметнатите продукти.',
      [
        { text: 'Изтрий отметнатите', onPress: actions.removeCheckedItems },
        {
          text: 'Изтрий целия списък',
          onPress: actions.clear,
          style: 'destructive',
        },
        {
          text: 'Откажи',
          onPress: () => console.log('Canceled shopping list deletion.'),
          style: 'cancel',
        },
      ],
    );
  };

  if (!hasItemsInShoppingList) {
    return null;
  }

  return (
    <TouchableOpacity style={tw`p-1`} onPress={handleDeletion}>
      <TrashIcon style={tw`mr-4`} size={25} color={theme.extend.colors.black} />
    </TouchableOpacity>
  );
};

export default DeleteItemsFromShoppingList;
