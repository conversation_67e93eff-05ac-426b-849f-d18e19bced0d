import { produce } from 'immer';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { showToast } from '@utils/in-app-notifications';
import { navigationRef } from '@utils/navigation';
import { storage } from '@utils/storage';

type ShoppingList = {
  [itemIdentifier: string]: Record<string, any>;
};

interface ShoppingListStore {
  list: ShoppingList;

  actions: {
    addToList: (recipe: string, ingredients: Record<string, any>[]) => void;
    removeCheckedItems: () => void;
    clear: () => void;
    toggleIngredient: (recipe: string, ingredient: string) => void;
  };
}

export const useShoppingListStore = create<ShoppingListStore>()(
  persist(
    set => ({
      list: {},

      actions: {
        addToList: (recipe, ingredients) => {
          set(
            produce(draft => {
              if (!draft.list[recipe]) {
                draft.list[recipe] = {};
              }

              for (const { key: ingredient, metric, imperial } of ingredients) {
                if (draft.list[recipe][ingredient]) continue;

                draft.list[recipe][ingredient] = {
                  metric,
                  imperial,
                  checked: false,
                };
              }
            }),
          );

          showToast({
            type: 'info',
            description: 'Успешно добавяне в списъка',
            onPress: () => navigationRef.navigate('ShoppingList' as never),
          });
        },
        removeCheckedItems: () =>
          set(
            produce(draft => {
              const list = draft.list;
              const isEmpty = Object.keys(list).length === 0;

              if (isEmpty) return draft;

              Object.entries(list).forEach(([key, value]) => {
                if (list[key] && Object.keys(list[key]).length > 0) {
                  Object.entries(value as Object).forEach(
                    ([ingredientKey, ingredientOptions]) => {
                      if (ingredientOptions.checked) {
                        delete list[key][ingredientKey];
                      }
                    },
                  );

                  if (Object.keys(list[key]).length === 0) {
                    delete list[key];
                  }
                }
              });
            }),
          ),
        toggleIngredient: (recipe, ingredientKey) =>
          set(
            produce(draft => {
              const shouldUpdate =
                draft.list[recipe] && draft.list[recipe][ingredientKey];

              if (!shouldUpdate) return;

              draft.list[recipe][ingredientKey].checked =
                !draft.list[recipe][ingredientKey].checked;
            }),
          ),
        clear: () => set({ list: {} }),
      },
    }),
    {
      name: 'veganna-shopping-list',
      storage: createJSONStorage(() => storage),
      partialize: state => ({ list: state.list }),
    },
  ),
);

export const useShoppingListActions = () =>
  useShoppingListStore(state => state.actions);
