import React from 'react';
import { TextInput, TouchableOpacity, View } from 'react-native';
import tw from '@utils/tailwind';
import Text from '@features/ui/shared/Text';
import { useShoppingListActions } from './shopping-list.store';

const AddOwnItemToShoppingList = () => {
  const [ingredient, setIngredient] = React.useState<string | undefined>();
  const actions = useShoppingListActions();

  const disabled = React.useMemo(
    () => !ingredient || ingredient === '',
    [ingredient],
  );

  const onPress = () => {
    if (!ingredient) {
      return;
    }

    actions.addToList('Моите продукти', [
      {
        key: ingredient,
        metric: ingredient,
        imperial: ingredient,
      },
    ]);

    setIngredient(undefined);
  };

  return (
    <View
      style={[
        tw`bg-background px-1 py-3 rounded-t-lg shadow-md flex-row justify-between h-18`,
      ]}>
      <TextInput
        style={tw`p-3 font-body-regular rounded-lg flex-shrink w-full`}
        placeholder="Добавете продукт по избор"
        placeholderTextColor={'rgba(81, 29, 25, 0.5)'}
        clearButtonMode="always"
        value={ingredient}
        onChangeText={setIngredient}
      />

      <TouchableOpacity
        onPress={onPress}
        disabled={disabled}
        style={[
          tw`rounded-lg ml-4 mr-2 self-center items-center justify-center`,
          tw.style(disabled ? 'bg-primary/50' : 'bg-primary'),
        ]}>
        <View>
          <Text
            style={tw`text-white text-center font-body-bold py-2 px-3 text-sm`}>
            Добави
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default AddOwnItemToShoppingList;
