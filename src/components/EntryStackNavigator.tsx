import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React from 'react';

import ForgottenPassword from '../screens/ForgottenPassword';
import Login from '../screens/Login';
import Onboarding from '../screens/Onboarding';
import Register from '../screens/Register';
import { usePersistedStore } from '../utils/store';
import LoggedInStackNavigator from './LoggedInStackNavigator';

const Stack = createNativeStackNavigator();

const EntryStackNavigator = () => {
  const token = usePersistedStore(state => state.token);
  const showWelcomeScreen = usePersistedStore(state => state.welcome);
  const hasSeenOnboarding = usePersistedStore(state => state.hasSeenOnboarding);

  const initialRouteName = React.useMemo(() => {
    if (token) return 'AppDashboard';
    if (!hasSeenOnboarding) return 'Onboarding';
    return 'Login';
  }, [hasSeenOnboarding, showWelcomeScreen, token]);

  return (
    <Stack.Navigator
      initialRouteName={initialRouteName}
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
      }}>
      {!hasSeenOnboarding ? (
        <Stack.Screen name="Onboarding" component={Onboarding} />
      ) : token ? (
        <Stack.Screen
          name="LoggedInStackNavigator"
          component={LoggedInStackNavigator}
        />
      ) : (
        <Stack.Group>
          <Stack.Screen name="Login" component={Login} />
          <Stack.Screen name="Register" component={Register} />
          <Stack.Screen
            name="ForgottenPassword"
            component={ForgottenPassword}
          />
        </Stack.Group>
      )}
    </Stack.Navigator>
  );
};

export default EntryStackNavigator;
