import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { View } from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import Container from '../features/ui/shared/Container';
import { useRefreshOnFocus } from '../utils/hooks';
import { getHomeData } from '../utils/queries';
import tw from '../utils/tailwind';
import DynamicHomeSection from './DynamicHomeSection';

const DynamicHomeContent = () => {
  const { data, refetch, isLoading } = useQuery(['home'], getHomeData);

  useRefreshOnFocus(refetch);

  if (isLoading)
    return (
      <Container style={tw`mt-6 mb-4 pb-4`}>
        <SkeletonPlaceholder borderRadius={4}>
          <View>
            <View style={tw`gap-y-6`}>
              <View style={tw`w-full h-[40px]`} />
              <View style={tw`w-full h-[240px]`} />
            </View>
            <View style={tw`mt-10 gap-y-10`}>
              <View style={tw`w-full h-[240px]`} />
            </View>
          </View>
        </SkeletonPlaceholder>
      </Container>
    );

  return data?.map(block => <DynamicHomeSection key={block.id} {...block} />);
};

export default DynamicHomeContent;
