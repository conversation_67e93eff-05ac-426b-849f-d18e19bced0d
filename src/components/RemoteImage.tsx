import React, { memo, useEffect } from 'react';
import {
  ImageStyle,
  LayoutChangeEvent,
  StyleProp,
  View,
  ViewStyle,
} from 'react-native';
import TurboImage from 'react-native-turbo-image';

import { FALLBACK_IMAGE_URI } from '../utils/api';
import tw from '../utils/tailwind';

type Props = {
  uri: string;
  containerStyle?: StyleProp<ViewStyle>;
  imageStyle?: StyleProp<ImageStyle>;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'center';
  handleError?: () => void;
  preload?: boolean;
};

/**
 * A component for displaying optimized remote images with caching
 */
const RemoteImage = ({
  uri,
  containerStyle,
  imageStyle: customImageStyle,
  resizeMode = 'cover',
  handleError,
  preload = true,
  children,
}: React.PropsWithChildren<Props>) => {
  const [layout, setLayout] = React.useState({ width: 0, height: 0 });

  const handleLayout = React.useCallback(
    (event: LayoutChangeEvent) => {
      const { width, height } = event.nativeEvent.layout;
      if (width !== layout.width || height !== layout.height) {
        setLayout({ width, height });
      }
    },
    [layout],
  );

  useEffect(() => {
    if (preload && uri) {
      TurboImage.prefetch([{ uri }]).catch(error => {
        console.error('[RemoteImage] Failed to preload image:', error);
        handleError?.();
      });
    }
  }, [uri, preload, handleError]);

  return (
    <View style={[tw`relative`, containerStyle]} onLayout={handleLayout}>
      {layout.width > 0 && layout.height > 0 && (
        <TurboImage
          source={{ uri: uri || FALLBACK_IMAGE_URI }}
          style={[
            tw`absolute inset-0`,
            { width: layout.width, height: layout.height },
            customImageStyle,
          ]}
          // Using a static blurhash placeholder, possibly until the api is configured
          placeholder={{ blurhash: 'LKO2?U%2Tw=w]~RBVZRi};RPxuwH' }}
          resizeMode={resizeMode}
          cachePolicy="urlCache"
          onFailure={handleError}
        />
      )}
      {children}
    </View>
  );
};

export default memo(RemoteImage);
