import tw from '@utils/tailwind';
import React, { useEffect } from 'react';
import { Pressable, Text } from 'react-native';
import { BellIcon } from 'react-native-heroicons/outline';
import Reanimated, {
  cancelAnimation,
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ToastConfig, ToastConfigParams } from 'react-native-toast-message';

type Props = {
  text1?: string;
  type?: 'info';
  onPress?: () => void;
};

const Toast: React.FC<Props & ToastConfigParams<any>> = ({
  text1 = '',
  type = 'info',
  onPress,
}) => {
  const insets = useSafeAreaInsets();
  const rotation = useSharedValue(0);
  const slideAnim = useSharedValue(-50);
  const opacityAnim = useSharedValue(0);

  const animatedContainerStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: slideAnim.value }],
    opacity: opacityAnim.value,
  }));

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }));

  useEffect(() => {
    if (type !== 'info') return;
    slideAnim.value = withTiming(0, {
      duration: 300,
      easing: Easing.out(Easing.ease),
    });
    opacityAnim.value = withTiming(1, {
      duration: 300,
      easing: Easing.out(Easing.ease),
    });
    rotation.value = withRepeat(
      withSequence(
        withTiming(20, { duration: 150, easing: Easing.out(Easing.ease) }),
        withTiming(-15, { duration: 150, easing: Easing.out(Easing.ease) }),
        withTiming(10, { duration: 120, easing: Easing.out(Easing.ease) }),
        withTiming(-8, { duration: 120, easing: Easing.out(Easing.ease) }),
        withTiming(5, { duration: 100, easing: Easing.out(Easing.ease) }),
        withTiming(-3, { duration: 100, easing: Easing.out(Easing.ease) }),
        withTiming(0, { duration: 80, easing: Easing.out(Easing.ease) }),
        withDelay(600, withTiming(0, { duration: 0 })),
      ),
      -1,
      false,
    );
    return () => cancelAnimation(rotation);
  }, [type]);

  return (
    <Reanimated.View
      style={[
        {
          position: 'absolute',
          top: insets.top > 46 ? 12 : -12,
          width: '70%',
          zIndex: 999,
        },
        animatedContainerStyle,
      ]}>
      <Pressable
        onPress={onPress}
        style={tw`flex-row items-center bg-white px-3 py-4 rounded-[32px] shadow-md shadow-black/70`}>
        <Reanimated.View style={[tw`ml-2 mr-4`, animatedStyle]}>
          <BellIcon size={24} color={'#D6837B'} />
        </Reanimated.View>

        <Text
          style={tw`flex-1 font-body-medium text-black text-[14px] leading-5 text-left`}>
          {text1}
        </Text>
      </Pressable>
    </Reanimated.View>
  );
};

export const toastConfig: ToastConfig = {
  info: props => <Toast {...props} type="info" />,
};
