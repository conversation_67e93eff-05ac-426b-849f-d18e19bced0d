import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { CheckCircleIcon } from 'react-native-heroicons/outline';
import { theme } from '../../../tailwind.config';
import Text from '../../features/ui/shared/Text';
import tw from '../../utils/tailwind';
import { PurchasesPackage } from 'react-native-purchases';

type Props = {
  isSelected: boolean;
  onSelect: () => void;
  subscriptionPackage: PurchasesPackage;
};

const PremiumOption = ({
  subscriptionPackage,
  isSelected,
  onSelect,
}: Props) => {
  const { title, priceString, introPrice, description } =
    subscriptionPackage.product;

  const renderIntroPrice = () => {
    if (!introPrice) return;

    const { priceString: price, periodUnit, periodNumberOfUnits } = introPrice;

    const getPeriodLabels = (): [[string, string], [string, string]] => {
      let p;

      switch (periodUnit) {
        case 'DAY':
          p = [
            ['първия', 'първите'],
            ['ден', 'дни'],
          ];
          return p;

        case 'WEEK':
          p = [
            ['първата', 'първите'],
            ['седмица', 'седмици'],
          ];
          return p;

        case 'MONTH':
          p = [
            ['първия', 'първите'],
            ['месец', 'месеца'],
          ];
          return p;

        case 'YEAR':
          p = [
            ['първата', 'първите'],
            ['година', 'години'],
          ];
          return p;
      }

      return p;
    };

    const [first, labels] = getPeriodLabels();

    const [fSingular, fPlural] = first;
    const [lSingular, lPlural] = labels;

    if (periodNumberOfUnits === 1) {
      return `${price} за ${fSingular + ' ' + lSingular}`;
    }

    return `${price} за ${fPlural} ${periodNumberOfUnits} ${lPlural}`;
  };

  return (
    <TouchableOpacity onPress={onSelect}>
      <View
        style={[
          tw`bg-white px-3 py-3 rounded-md mb-3`,
          tw.style(isSelected ? 'border-2' : 'border'),
          tw.style(isSelected && 'shadow-lg shadow-primary'),
          tw.style(isSelected ? 'border-primary' : 'border-primary/30'),
        ]}>
        <Text
          style={[
            tw`text-base`,
            tw.style(isSelected ? 'font-body-semibold' : 'font-normal'),
          ]}>
          {title}
        </Text>

        <Text
          style={[
            tw`text-black text-sm`,
            tw.style(isSelected ? 'font-body-semibold' : 'font-normal'),
          ]}>
          {introPrice
            ? `${renderIntroPrice()}, \nслед това ${priceString}`
            : priceString}
        </Text>

        {isSelected && (
          <View style={tw`absolute right-2 top-2`}>
            <CheckCircleIcon stroke={theme.extend.colors.primary} />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default PremiumOption;
