import React from 'react';
import { ActivityIndicator, View } from 'react-native';
import { theme } from '../../../tailwind.config';
import tw from '../../utils/tailwind';
import PremiumOption from './PremiumOption';

const PremiumOptions = props => {
  if (!props.selectedOption || !props.options || props.options.length === 0) {
    return (
      <View style={tw`items-center justify-center my-4`}>
        <ActivityIndicator size={'small'} color={theme.extend.colors.primary} />
      </View>
    );
  }

  return (
    <View style={tw`w-full`}>
      {props.options?.map(subscriptionPackage => (
        <PremiumOption
          onSelect={() => props.onSelect(subscriptionPackage)}
          isSelected={
            subscriptionPackage.identifier === props.selectedOption?.identifier
          }
          key={subscriptionPackage.identifier}
          subscriptionPackage={subscriptionPackage}
        />
      ))}
    </View>
  );
};

export default PremiumOptions;
