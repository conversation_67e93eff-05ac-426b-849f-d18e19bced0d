import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { TouchableOpacity } from 'react-native';

import { FALLBACK_IMAGE_URI } from '../../../utils/api';
import tw from '../../../utils/tailwind';
import RemoteImage from '../../RemoteImage';

type Props = {
  bookId: number;
  bookCover?: string;
};

const BookBanner = (props: Props) => {
  const navigation = useNavigation();

  const navigateToBook = () =>
    //@ts-ignore
    navigation.navigate('Book', {
      bookId: props.bookId,
    });

  return (
    <TouchableOpacity onPress={navigateToBook} style={tw`bg-red-100 w-full`}>
      <RemoteImage
        uri={props?.bookCover || FALLBACK_IMAGE_URI}
        containerStyle={tw`bg-gray-400 w-full h-[80px]`}
        resizeMode="cover"
      />
    </TouchableOpacity>
  );
};

export default BookBanner;
