import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { getBooks } from '../../utils/queries';
import Container from '../../features/ui/shared/Container';
import BookBanner from './BookBanner';
import LoadingBookBanner from './LoadingBookBanner';
import { useRefreshOnFocus } from '../../utils/hooks';
import tw from '../../utils/tailwind';

const BookBanners = () => {
  const { data: books, isLoading, refetch } = useQuery(['books'], getBooks);

  useRefreshOnFocus(refetch);

  if (isLoading) return <LoadingBookBanner />;

  if (books && !books.length) return null;

  return (
    <Container style={tw`gap-y-6`}>
      {books?.map(book => (
        <BookBanner key={book.id} bookId={book.id} bookCover={book?.banner} />
      ))}
    </Container>
  );
};

export default BookBanners;
