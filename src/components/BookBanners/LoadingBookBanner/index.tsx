import { View, Text } from 'react-native';
import React from 'react';
import Container from '../../../features/ui/shared/Container';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import tw from '../../../utils/tailwind';

const LoadingBookBanner = () => {
  return (
    <Container>
      <SkeletonPlaceholder borderRadius={4}>
        <View>
          <View style={tw`gap-y-4`}>
            <View style={tw`w-full h-[80px]`} />
            <View style={tw`w-full h-[80px]`} />
            <View style={tw`w-full h-[80px]`} />
          </View>
        </View>
      </SkeletonPlaceholder>
    </Container>
  );
};

export default LoadingBookBanner;
