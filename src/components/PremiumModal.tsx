import React from 'react';
import { Modal, View } from 'react-native';

import { useDataStore } from '@utils/store';
import tw from '../utils/tailwind';
import GetPremium from './GetPremium';

const PremiumModal = () => {
  const visible = useDataStore(state => state.shouldShowPremiumModal);
  const setVisible = useDataStore(state => state.setShouldShowPremiumModal);

  return (
    <Modal
      animationType="slide"
      presentationStyle="formSheet"
      visible={visible}
      onRequestClose={() => {
        setVisible(!visible);
      }}>
      <View style={tw`bg-white flex-1`}>
        <GetPremium />
      </View>
    </Modal>
  );
};

export default PremiumModal;
