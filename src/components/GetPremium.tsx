import React from 'react';
import { Alert, View } from 'react-native';
import Purchases from 'react-native-purchases';

import { ActionButton } from '@features/ui/button';
import CrownIcon from '../assets/svg/CrownIcon';
import Container from '../features/ui/shared/Container';
import Heading from '../features/ui/shared/Heading';
import Text from '../features/ui/shared/Text';
import { axiosInstance, updateSubscriptionStatusManually } from '../utils/api';
import { ENTITLEMENT_ID } from '../utils/iap';
import { useDataStore, usePersistedStore } from '../utils/store';
import tw from '../utils/tailwind';
import PremiumOptions from './Subscription/PremiumOptions';

const GetPremium = () => {
  const token = usePersistedStore(state => state.token);

  const [loading, setLoading] = React.useState<boolean>(false);
  const [packages, setPackages] = React.useState<any>();
  const [selectedPackage, setSelectedPackage] = React.useState<any>();

  const setIsUserPremium = useDataStore(state => state.setIsUserPremium);
  const setShouldShowPremiumModal = useDataStore(
    state => state.setShouldShowPremiumModal,
  );

  const getOfferings = async () => {
    try {
      if (await Purchases.isConfigured()) {
        const offerings = (await Purchases.getOfferings()) || [];
        setPackages(offerings.current?.availablePackages);
        setSelectedPackage(offerings.current?.availablePackages[1]);
      }
    } catch (error) {
      Alert.alert('Възникна грешка, извличайки абонаментните планове.');
    }
  };

  React.useEffect(() => {
    getOfferings();
  }, []);

  const makePurchase = async () => {
    if (!selectedPackage || !(await Purchases.isConfigured())) {
      return;
    }

    try {
      setLoading(true);

      const userData = await axiosInstance.get('/me');

      if (!userData?.data?.uuid) {
        throw new Error(
          'Възникна грешка, моля излезте от акаунта си и опитайте пак.',
        );
      }

      await Purchases.logIn(userData?.data.uuid);

      const { customerInfo } = await Purchases.purchasePackage(selectedPackage);

      const subscribed =
        typeof customerInfo.entitlements.active[ENTITLEMENT_ID] !== 'undefined';

      setIsUserPremium(subscribed);

      await updateSubscriptionStatusManually(token, subscribed);

      if (subscribed) {
        setShouldShowPremiumModal(false);
      }
    } catch (e: any) {
      if (!e.userCancelled) {
        Alert.alert(e);
      } else {
        Alert.alert(e?.message);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container style={tw`py-8`}>
      <View style={tw`justify-center items-center`}>
        <CrownIcon />
        <Heading style={tw`my-2 text-4xl`}>ПРИСЪЕДИНИ СЕ КЪМ НАС</Heading>
        <Text style={tw`text-center`}>
          Отключи премиум абонамент и се наслаждавай на всички функции на
          приложението! 500+ здравословни и вкусни рецепти + нови рецепти всяка
          седмица!
        </Text>
      </View>

      <View style={tw`my-6`}>
        <PremiumOptions
          options={packages}
          selectedOption={selectedPackage}
          onSelect={setSelectedPackage}
        />
      </View>
      <ActionButton
        disabled={!selectedPackage || loading}
        loading={loading}
        onPress={makePurchase}
        label={'Отключи всички рецепти →'}
      />
    </Container>
  );
};

export default GetPremium;
