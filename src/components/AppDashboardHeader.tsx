import React from 'react';
import UserIcon from '../features/profile/UserIcon';
import tw from '../utils/tailwind';
import Container from '../features/ui/shared/Container';
import Greeting from '../features/profile/Greeting';
import { View, ViewProps } from 'react-native';

const AppDashboardHeader = (props: ViewProps) => {
  return (
    <View {...props}>
      <Container style={tw`flex-row justify-between items-start`}>
        <Greeting />
        <UserIcon />
      </Container>
    </View>
  );
};

export default AppDashboardHeader;
