import React from 'react';
import { Linking, Pressable, View } from 'react-native';
import { ChevronRightIcon } from 'react-native-heroicons/outline';

import tailwindConfig from '../../tailwind.config';
import Text from '../features/ui/shared/Text';
import { FALLBACK_IMAGE_URI } from '../utils/api';
import tw from '../utils/tailwind';
import { SocialBlockItem } from './DynamicHomeSection';
import RemoteImage from './RemoteImage';

const FeaturedBlockItem = (props: SocialBlockItem) => {
  const navigate = React.useCallback(async () => {
    if (await Linking.canOpenURL(props?.social_media_url)) {
      await Linking.openURL(props?.social_media_url);
    }
  }, [props?.social_media_url]);

  return (
    <View style={tw`h-[400px] w-[350px] mr-6 bg-white shadow-md rounded-b-lg`}>
      <Pressable onPress={navigate}>
        <RemoteImage
          uri={props?.image ?? FALLBACK_IMAGE_URI}
          containerStyle={tw`h-[320px] w-full rounded-t-lg`}
        />

        <View
          style={tw`p-4 bg-white rounded-b-lg h-[80px] flex-row items-center justify-between`}>
          <View>
            <Text
              lineBreakMode="tail"
              numberOfLines={1}
              style={tw`text-base mt-1 font-medium`}>
              {props.title}
            </Text>

            <Text
              lineBreakMode="tail"
              numberOfLines={2}
              style={tw`text-sm opacity-80`}>
              {props.username}
            </Text>
          </View>

          <ChevronRightIcon
            size={25}
            strokeWidth={2}
            color={tailwindConfig.theme.extend.colors.black}
          />
        </View>
      </Pressable>
    </View>
  );
};

export default React.memo(FeaturedBlockItem);
