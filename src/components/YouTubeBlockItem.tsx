import React from 'react';
import { Linking, Pressable, View } from 'react-native';

import Text from '../features/ui/shared/Text';
import { FALLBACK_IMAGE_URI } from '../utils/api';
import tw from '../utils/tailwind';
import { YouTubeContentBlock } from './DynamicHomeSection';
import RemoteImage from './RemoteImage';

const YouTubeBlockItem = (props: YouTubeContentBlock) => {
  const navigate = React.useCallback(async () => {
    if (await Linking.canOpenURL(props?.url)) {
      await Linking.openURL(props?.url);
    }
  }, [props?.url]);

  return (
    <View style={tw`w-[200px] mr-4`}>
      <Pressable onPress={navigate}>
        <RemoteImage
          uri={props?.thumbnail_url ?? FALLBACK_IMAGE_URI}
          containerStyle={tw`h-[110px] w-[200px] rounded-md`}
          resizeMode="cover"
        />
        <View style={tw`self-start`}>
          <Text
            lineBreakMode="tail"
            numberOfLines={2}
            style={tw`text-sm mt-1 font-medium`}>
            {props.title}
          </Text>
        </View>
      </Pressable>
    </View>
  );
};

export default React.memo(YouTubeBlockItem);
