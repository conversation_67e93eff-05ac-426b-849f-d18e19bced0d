import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { Pressable } from 'react-native';
import { useDataStore } from '../utils/store';

export default (RecipeItemComponent: any) => {
  function WrappedComponent(props) {
    const navigation = useNavigation<any>();
    const ownedBooks = useDataStore(state => state.ownedBooks);
    const isUserPremium = useDataStore(state => state.isUserPremium);
    const showPremiumModal = useDataStore(
      state => state.setShouldShowPremiumModal,
    );

    const isPartOfBook = props.books && props.books.length > 0;

    async function goToRecipe() {
      const isRecipePremium = !!props?.premium;

      if (isRecipePremium && !isUserPremium) {
        showPremiumModal(true);
        return;
      }

      if (isPartOfBook) {
        if (!props.books) return;

        const inaccessibleBooks = props.books.filter(
          book => !ownedBooks.includes(book.id),
        );

        if (inaccessibleBooks.length > 0) {
          const book = inaccessibleBooks[0];
          navigation.navigate('Questionnaire', {
            bookId: book.id,
            timeout: book.questionnaire_timeout,
          });
          return;
        }
      }

      navigation.navigate('Recipe', {
        id: props.id,
        title: props.title,
        premium: props.premium,
      });
    }

    return (
      <Pressable onPress={goToRecipe}>
        <RecipeItemComponent {...props} />
      </Pressable>
    );
  }

  return WrappedComponent;
};
