import React from 'react';
import { View } from 'react-native';
import { CheckIcon } from 'react-native-heroicons/outline';
import tw from '../utils/tailwind';

const CheckBox = ({ selected, size = 'md' }) => {
  const sizeOptions = React.useMemo(
    () => ({
      sm: { container: 'w-3.5 h-3.5', bg: 'w-3 h-3', icon: 10 },
      md: { container: 'w-5.5 h-5.5', bg: 'w-5 h-5', icon: 13 },
      lg: { container: 'w-3 h-3', bg: 'w-7 h-7', icon: 20 },
    }),
    [],
  );
  return (
    <View style={tw`items-center justify-center`}>
      <View
        style={[
          tw`bg-primary rounded-full`,
          tw.style(sizeOptions[size].container),
        ]}
      />
      <View
        style={[
          tw`absolute rounded-full items-center justify-center`,

          tw.style(sizeOptions[size].bg),
          tw.style(selected ? 'bg-primary' : 'bg-white'),
        ]}>
        {selected && (
          <CheckIcon
            size={sizeOptions[size].icon}
            stroke="#fff"
            strokeWidth={3}
          />
        )}
      </View>
    </View>
  );
};

export default CheckBox;
