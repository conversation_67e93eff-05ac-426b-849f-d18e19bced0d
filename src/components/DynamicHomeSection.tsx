import React, { useCallback } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>View, View } from 'react-native';

import { Recipe } from '@features/recipes';
import { ActionButton } from '@features/ui/button';
import RecipeCarouselItem from '../features/recipes/RecipeCarouselItem';
import Container from '../features/ui/shared/Container';
import Heading from '../features/ui/shared/Heading';
import Text from '../features/ui/shared/Text';
import { FALLBACK_IMAGE_URI } from '../utils/api';
import tw from '../utils/tailwind';
import FeaturedBlockItem from './FeaturedBlockItem';
import RemoteImage from './RemoteImage';
import YouTubeBlockItem from './YouTubeBlockItem';

export type YouTubeContentBlock = {
  url: string;
  title: string;
  thumbnail_url: string;
};

export type SocialBlockItem = {
  title: string;
  image: string;
  username: string;
  social_media_url: string;
};

type FeaturedContentBlock = {
  social: SocialBlockItem[];
  recipes: Recipe[];
};

type InfoContentBlock = {
  title?: string;
  text?: string;
  image?: string;
  link_label?: string;
  link_href?: string;
};

interface BaseProps {
  id: number;
  title?: string;
  size: 'base' | 'lg';
}

interface RecipeBlockProps extends BaseProps {
  type: 'recipes_block';
  content: Recipe[];
}

interface FeaturedBlockProps extends BaseProps {
  type: 'featured_block';
  content: FeaturedContentBlock;
}

interface YouTubeBlockProps extends BaseProps {
  type: 'youtube_block';
  content: YouTubeContentBlock[];
}

interface InfoBlockProps extends BaseProps {
  type: 'info_block';
  content: InfoContentBlock;
}

interface DividerProps extends BaseProps {
  type: 'divider';
  content: [];
}

type DynamicHomeSectionProps =
  | RecipeBlockProps
  | FeaturedBlockProps
  | YouTubeBlockProps
  | InfoBlockProps
  | DividerProps;

const DynamicHomeSection = (props: DynamicHomeSectionProps) => {
  const renderContent = () => {
    switch (props?.type) {
      case 'recipes_block':
        return renderRecipeBlock(props?.content);
      case 'youtube_block':
        return renderYouTubeBlock(props?.content);
      case 'featured_block':
        return renderFeaturedBlock(props?.content);
      case 'info_block':
        return renderInfoBlock(props?.content);
      case 'divider':
        return renderDivider();
      default:
        return null;
    }
  };

  const renderDivider = () => (
    <View
      style={tw`h-[3px] my-4 bg-background rounded-full w-2/3 mx-auto`}></View>
  );

  const renderInfoBlock = function <T extends InfoContentBlock>(data: T) {
    return (
      <Container style={tw`py-12 mt-8 bg-background/50`}>
        {data?.image ? (
          <RemoteImage
            uri={data?.image ?? FALLBACK_IMAGE_URI}
            containerStyle={tw`h-[300px] w-full`}
            imageStyle={tw`rounded-2xl`}
          />
        ) : null}

        <View
          style={[tw`w-full flex-shrink`, tw.style(data?.image ? 'mt-4' : '')]}>
          {data?.title ? (
            <Heading style={tw`text-4xl`}>{data?.title}</Heading>
          ) : null}
          {data?.text ? (
            <Text style={tw`font-body-medium mt-2 text-base`}>
              {data?.text}
            </Text>
          ) : null}
        </View>

        {data?.link_label && data?.link_href ? (
          <ActionButton
            label={data?.link_label}
            style={tw`w-full mt-6`}
            onPress={async () => {
              if (await Linking?.canOpenURL(data?.link_href!)) {
                await Linking?.openURL(data?.link_href!);
              }
            }}
          />
        ) : null}
      </Container>
    );
  };
  const renderFeaturedBlock = function <T extends FeaturedContentBlock>(
    data: T,
  ) {
    return (
      <View style={tw`flex-row`}>
        <ScrollView
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={tw`pb-4 px-6`}
          alwaysBounceVertical={false}>
          {data?.recipes?.map(recipe => (
            <RecipeCarouselItem
              key={recipe.id}
              {...recipe}
              large
              isBookLabelVisible
            />
          ))}
          {data?.social?.map(social => (
            <FeaturedBlockItem key={social.social_media_url} {...social} />
          ))}
        </ScrollView>
      </View>
    );
  };

  // According to multiple issues, FlashList has several bugs when used
  // with horizontal lists

  const renderYouTubeBlock = function <T extends YouTubeContentBlock[]>(
    videos: T,
  ) {
    const renderItem = useCallback(
      ({ item: video }: { item: YouTubeContentBlock }) => (
        <YouTubeBlockItem {...video} />
      ),
      [],
    );

    return (
      <FlatList
        data={videos}
        renderItem={renderItem}
        keyExtractor={item => item?.url}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={tw`pb-4 px-6`}
      />
    );
  };

  const renderRecipeBlock = function <T extends Recipe[]>(recipes: T) {
    const renderItem = useCallback(
      ({ item: recipe }: { item: Recipe }) => (
        <RecipeCarouselItem {...recipe} />
      ),
      [],
    );

    return (
      <FlatList
        data={recipes}
        renderItem={renderItem}
        keyExtractor={item => item?.id?.toString()}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={tw`pb-4 px-6`}
      />
    );
  };

  const isDisabledToRenderLists = React.useMemo(() => {
    return (
      (props?.type === 'recipes_block' || props?.type === 'youtube_block') &&
      !props?.content?.length
    );
  }, [props?.type, props?.content]);

  if (!props?.content) return null;

  if (isDisabledToRenderLists) return null;

  return (
    <View>
      {props?.title ? (
        <Container
          style={tw`mt-6 mb-4 flex-row justify-between items-baseline`}>
          <Heading style={tw`text-4xl`}>{props.title}</Heading>
        </Container>
      ) : null}

      {renderContent()}
    </View>
  );
};

export default DynamicHomeSection;
