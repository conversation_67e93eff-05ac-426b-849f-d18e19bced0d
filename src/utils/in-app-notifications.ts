import Toast from 'react-native-toast-message';

export type ToastOptions = {
  type: 'info' | 'error';
  description: string;
  autoHide?: boolean;
  visibilityTime?: number;
  onPress?: () => void;
};

const defaultOptions = {
  autoHide: true,
  visibilityTime: 4000,
};

export const showToast = ({
  type,
  description,
  autoHide,
  visibilityTime,
  onPress,
}: ToastOptions): void => {
  const handleOnPress = () => {
    if (onPress) {
      onPress();
      Toast.hide();
    }
  };

  Toast.show({
    ...defaultOptions,
    type,
    text1: description,
    position: 'top',
    autoHide,
    visibilityTime,
    onPress: onPress ? handleOnPress : undefined,
  });
};
