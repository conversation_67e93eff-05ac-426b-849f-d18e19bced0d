import Purchases from 'react-native-purchases';
import { axiosInstance } from './api';
import { usePersistedStore } from './store';

export enum UserRole {
  ADMIN = 1,
  STANDARD = 2,
  PREMIUM = 3,
}

async function register({
  name,
  email,
  password,
  password_confirmation,
  device_name,
}) {
  try {
    const registerResponse = await axiosInstance.post('/register', {
      name,
      email,
      password,
      password_confirmation,
      device_name,
    });

    return registerResponse.data.token;
  } catch (error) {
    throw error;
  }
}

async function forgotPassword({ email }) {
  try {
    await axiosInstance.post('/forgot-password', {
      email,
    });
  } catch (error) {
    throw error;
  }
}

async function logout() {
  try {
    const setPersistedStore = usePersistedStore.getState().setPersistedStore;

    await axiosInstance.post('/logout');

    await Purchases.logOut();

    setPersistedStore('token', undefined);
    setPersistedStore('user', undefined);
  } catch (error) {
    console.error('Error during logout', error);
  }
}

async function login({ email, password, device_name }) {
  try {
    const loginResponse = await axiosInstance.post('/login', {
      email,
      password,
      device_name,
    });

    return loginResponse.data.token;
  } catch (error) {
    throw error;
  }
}

async function updateUserInfo({ name }) {
  try {
    const updateUserInfoResponse = await axiosInstance.put('/me', {
      name,
    });

    return updateUserInfoResponse.data;
  } catch (error) {
    throw error;
  }
}

async function changePassword({
  password,
  new_password,
  new_password_confirmation,
}) {
  try {
    const changePasswordResponse = await axiosInstance.post(
      '/change-password',
      {
        password,
        new_password,
        new_password_confirmation,
      },
    );

    return changePasswordResponse.data;
  } catch (error) {
    throw error;
  }
}

export {
  login,
  logout,
  register,
  forgotPassword,
  updateUserInfo,
  changePassword,
};
