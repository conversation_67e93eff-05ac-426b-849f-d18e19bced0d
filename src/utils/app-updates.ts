import remoteConfig from '@react-native-firebase/remote-config';
import { Platform } from 'react-native';
import { checkVersion } from 'react-native-check-version';
import DeviceInfo from 'react-native-device-info';

import { useDataStore } from './store';

/**
 * Checks if the app needs an update by comparing the current version with
 * the latest version from the app store. Also checks Firebase Remote Config
 * for critical update flags.
 */
export const checkForUpdates = async (): Promise<void> => {
  try {
    const currentVersion = DeviceInfo.getVersion();
    const bundleId = DeviceInfo.getBundleId();

    const result = await checkVersion({
      platform: Platform.OS,
      currentVersion,
      bundleId,
    });

    if (result.url) {
      useDataStore.setState({ updateUrl: result.url });
    }

    if (result.needsUpdate) {
      await remoteConfig().fetchAndActivate();
      const isCritical = remoteConfig().getValue('critical_update').asBoolean();

      useDataStore.setState({
        isCriticalUpdate: isCritical,
        shouldShowAppUpdateScreen: true,
      });
    }
  } catch (error) {
    console.error('[app-updates] Error checking for updates:', error);
  }
};
