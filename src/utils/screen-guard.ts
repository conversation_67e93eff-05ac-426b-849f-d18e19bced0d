import remoteConfig from '@react-native-firebase/remote-config';
import { Platform } from 'react-native';
import ScreenGuardModule from 'react-native-screenguard';
import type { ScreenGuardColorData } from 'react-native-screenguard/src/types/data';
import { me } from './queries';

type AuthorizedUsersConfig = {
  emails: string[];
};

const PARAMETER_KEY = 'screen_guard_authorized_users';
const MAX_RETRIES = 3;

const SCREEN_GUARD_CONFIG: ScreenGuardColorData = {
  backgroundColor: '#FFFFFF',
  timeAfterResume: 2000,
} as const;

/**
 * Setups screen guard based on Firebase Remote Config fetched emails: users with
 * authorized emails can record/screenshot, others cannot.
 */
export const setupScreenGuard = async (): Promise<void> => {
  const authorizedEmails = await fetchAuthorizedEmails();
  const user = await me();

  if (!user) return;

  if (authorizedEmails.includes(user.email)) {
    // Unregister for active sessions
    ScreenGuardModule.unregister();
    // console.log('[setupScreenGuard] Screen guard is deactivated');
  } else {
    if (Platform.OS === 'android') {
      // Some native modules/APIs (e.g. Alert.alert) interfere with
      // the screen guard. We use `registerWithoutEffect` to avoid the issue
      ScreenGuardModule.registerWithoutEffect();
    } else if (Platform.OS === 'ios') {
      ScreenGuardModule.register(SCREEN_GUARD_CONFIG);
    }
    // console.log('[setupScreenGuard] Screen guard is activated');
  }
};

const fetchAuthorizedEmails = async (retryCount = 0): Promise<string[]> => {
  const getBackoffDelay = (attempt: number) => {
    const baseDelayInMs = 1_000;
    const maxDelayInMs = 10_000;
    const jitter = Math.random() * 1000; // Random delay between 0-1000ms
    return Math.min(
      baseDelayInMs * Math.pow(2, attempt) + jitter,
      maxDelayInMs,
    );
  };

  try {
    await remoteConfig().fetchAndActivate();
    // Utilize the newly fetched configuration data, or fallback to the
    // cached data if no new data is available.
    const value = remoteConfig().getValue(PARAMETER_KEY);
    const parsed = JSON.parse(value.asString()) as AuthorizedUsersConfig;
    return parsed?.emails ?? [];
  } catch (error) {
    if (retryCount < MAX_RETRIES) {
      await new Promise(resolve =>
        setTimeout(resolve, getBackoffDelay(retryCount)),
      );
      return fetchAuthorizedEmails(retryCount + 1);
    }

    const value = remoteConfig().getValue(PARAMETER_KEY);
    const parsed = JSON.parse(value.asString()) as AuthorizedUsersConfig;
    return parsed?.emails ?? [];
  }
};
