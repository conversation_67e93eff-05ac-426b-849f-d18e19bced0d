import remoteConfig from '@react-native-firebase/remote-config';

const REMOTE_CONFIG_DEFAULTS = {
  screen_guard_authorized_users: JSON.stringify({ emails: [] }),
} as const;

/**
 * Initializes Firebase Remote Config with default settings and values.
 */
export const initFirebaseRemoteConfig = async (): Promise<void> => {
  try {
    await remoteConfig()
      .setDefaults(REMOTE_CONFIG_DEFAULTS)
      .then(() =>
        remoteConfig().setConfigSettings({
          minimumFetchIntervalMillis: __DEV__ ? 0 : 43_200_000, // 0 for dev, 12 hours for prod
        }),
      );
  } catch (error) {
    console.error('[initFirebaseRemoteConfig] Initialization error:', error);
  }
};
