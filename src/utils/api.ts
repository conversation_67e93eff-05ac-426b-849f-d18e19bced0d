import axios, { AxiosError } from 'axios';

import { usePersistedStore } from './store';
import { me } from './queries';

export const BASE_URL = 'https://panel.veganna.bg';

export const FALLBACK_IMAGE_URI = 'https://via.placeholder.com/200';

const axiosInstance = axios.create({
  baseURL: `${BASE_URL}/api/`,
  timeout: 20000,
});

axiosInstance.interceptors.request.use(function (config) {
  const token = usePersistedStore.getState().token;

  if (token) {
    (config.headers as any).Authorization = `Bearer ${token}`;
  }

  return config;
});

axiosInstance.interceptors.response.use(
  function (response) {
    // Any status code that lie within the range of 2xx cause this function to trigger
    // Do something with response data
    return response;
  },
  function (error) {
    if (!axios.isAxiosError(error)) {
      console.error('Regular error');
      console.error(error);
    }

    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      usePersistedStore.getState().setPersistedStore('token', undefined);
    }

    console.log(axiosError.response?.status);

    return Promise.reject(error);
  },
);

export { axiosInstance };

/* ---------------------------------- TEMP ---------------------------------- */

export const updateSubscriptionStatusManually = async (
  token: string | undefined,
  isUserPremium: boolean,
) => {
  if (!token) return;

  const user = await me();

  if (isUserPremium && user.role_id === 2) {
    console.log(
      'Updating subscription status manually',
      isUserPremium,
      user.role_id,
    );
    return axios
      .post(
        `${BASE_URL}/api/revcat/webhook`,
        {
          event: {
            type: 'INITIAL_PURCHASE', // The event type doesn't matter, we need `role_id` to be set to 3
            environment: 'PRODUCTION',
            app_user_id: user.uuid,
          },
        },
        {
          headers: {
            Authorization: `Bearer vO7ILmg8dshS1Inkrl7RLMYMaHGGhd2W`,
          },
        },
      )
      .then(res => res.data)
      .catch(async error => {
        console.warn(
          '[updateSubscriptionStatusManually] Error updating subscription status:',
          error?.response?.data || error?.message,
        );
      });
  }
};
