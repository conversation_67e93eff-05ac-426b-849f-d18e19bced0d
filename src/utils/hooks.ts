import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import React, { useEffect, useMemo, useState } from 'react';
import {
  AppState,
  AppStateStatus,
  InteractionManager,
  Platform,
  useWindowDimensions,
} from 'react-native';
import CodePush from 'react-native-code-push';
import { OneSignal } from 'react-native-onesignal';
import Purchases, { CustomerInfo } from 'react-native-purchases';

import { updateSubscriptionStatusManually } from './api';
import { APPLE_API_KEY, ENTITLEMENT_ID, GOOGLE_API_KEY } from './iap';
import { getBook, me } from './queries';
import { useDataStore, usePersistedStore } from './store';

export function useRefreshOnFocus<T>(refetch: () => Promise<T>) {
  const firstTimeRef = React.useRef(true);

  useFocusEffect(
    React.useCallback(() => {
      if (firstTimeRef.current) {
        firstTimeRef.current = false;
        return;
      }

      refetch();
    }, [refetch]),
  );
}

export function useMe() {
  const setOwnedBooks = useDataStore(state => state.setOwnedBooks);
  const query = useQuery(['me'], me);

  useEffect(() => {
    if (query.data && query.data.books) {
      setOwnedBooks(query.data.books.map(book => book.id));
    }
  }, [query.data]);

  return query;
}

export function useDebounce(value, delay) {
  // State and setters for debounced value
  const [debouncedValue, setDebouncedValue] = React.useState(value);
  React.useEffect(
    () => {
      // Update debounced value after delay
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);
      // Cancel the timeout if value changes (also on delay change or unmount)
      // This is how we prevent debounced value from updating if value is changed ...
      // .. within the delay period. Timeout gets cleared and restarted.
      return () => {
        clearTimeout(handler);
      };
    },
    [value, delay], // Only re-call effect if value or delay changes
  );
  return debouncedValue;
}

export const useRecipeAccess = ({ recipeIsPremium = false }) => {
  const navigation = useNavigation<any>();

  const isUserPremium = useDataStore(state => state.isUserPremium);
  const setShouldShowPremiumModal = useDataStore(
    state => state.setShouldShowPremiumModal,
  );

  useFocusEffect(
    React.useCallback(() => {
      const task = InteractionManager.runAfterInteractions(() => {
        if (!isUserPremium && recipeIsPremium) {
          setTimeout(() => {
            navigation.replace('AppDashboard');
            setShouldShowPremiumModal(true);
          }, 500);
        }
      });

      return () => task.cancel();
    }, [navigation, isUserPremium, recipeIsPremium]),
  );

  return isUserPremium;
};

export const useCodePush = () => {
  const [updating, setUpdating] = useState(false);
  const appState = useAppState();

  useEffect(() => {
    if (__DEV__) return;

    const handleBundleUpdates = async () => {
      try {
        await CodePush.notifyAppReady();

        const remotePackage = await CodePush.checkForUpdate();

        if (!remotePackage) return;

        setUpdating(true);

        const localPackage = await remotePackage.download();

        if (!localPackage) return;

        await localPackage.install(CodePush.InstallMode.ON_NEXT_RESUME);

        CodePush.restartApp(true);
      } finally {
        setUpdating(false);
      }
    };

    if (appState === 'active') {
      handleBundleUpdates();
    }
  }, [appState]);

  return {
    isAppUpdating: updating,
  };
};

export function useAppState() {
  const [appState, setAppState] = React.useState<AppStateStatus>(
    AppState.currentState,
  );

  React.useEffect(() => {
    function onChange(newState: AppStateStatus) {
      setAppState(newState);
    }

    const subscription = AppState.addEventListener('change', onChange);

    return () => {
      subscription.remove();
    };
  }, []);

  return appState;
}

export function useRecipes() {}

export function useBook(bookId: string) {
  const { data: user, isLoading: isUserLoading } = useQuery(['me'], me);

  const { data: book, isLoading: isBookLoading } = useQuery({
    queryKey: ['book', bookId],
    queryFn: getBook,
    enabled: !!user,
  });

  const isUserOwnedBook = user?.books.find(book => bookId === book.id);

  return {
    isLoading: isUserLoading || isBookLoading,
    book,
    isUserOwnedBook,
  };
}

export const useIAP = () => {
  const token = usePersistedStore(state => state.token);
  const setIsUserPremium = useDataStore(state => state.setIsUserPremium);

  React.useEffect(() => {
    if (Platform.OS === 'ios') {
      Purchases.configure({ apiKey: APPLE_API_KEY });
    } else if (Platform.OS === 'android') {
      Purchases.configure({ apiKey: GOOGLE_API_KEY });
    }

    Purchases.addCustomerInfoUpdateListener((info: CustomerInfo) => {
      // handle any changes to customerInfo
      const isSubscribed =
        typeof info.entitlements.active[ENTITLEMENT_ID] !== 'undefined';
      OneSignal.User.addTag('subscribed', isSubscribed ? 'true' : 'false');
      setIsUserPremium(isSubscribed);

      if (token) {
        updateSubscriptionStatusManually(token, isSubscribed);
      }

      // console.log('User ' + (isSubscribed ? 'is' : 'is not') + ' subscribed!');
    });
  }, []);
};

export const useScreenSize = (): { isSmallScreen: boolean } => {
  const { height } = useWindowDimensions();

  const isSmallScreen = useMemo(() => {
    if (height > 812) {
      // iPhone 11 Pro and larger
      return false;
    } else {
      // iPhone SE and smaller
      return true;
    }
  }, [height]);

  return { isSmallScreen };
};
