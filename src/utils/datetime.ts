export function convertTime(seconds: number) {
  let hours = Math.floor(seconds / 3600);
  let minutes = Math.floor((seconds - hours * 3600) / 60);
  seconds = seconds - hours * 3600 - minutes * 60;
  if (!!hours) {
    if (!!minutes) {
      return `${hours} ч. ${minutes} мин. ${seconds} сек.`;
    } else {
      return `${hours} ч. ${seconds} сек.`;
    }
  }
  if (!!minutes) {
    return `${minutes} мин. ${seconds} сек.`;
  }
  return `${seconds} сек.`;
}
