import {
  createNavigationContainerRef,
  LinkingOptions,
} from '@react-navigation/native';
import { Linking } from 'react-native';

type RecipeScreenParams = {
  id: number;
  premium: boolean;
  title?: string;
};

type LoggedInStackParamList = {
  Recipe: RecipeScreenParams;
  Recipes: undefined;
};

export type RootStackParamList = {
  EntryStackNavigator: undefined;
  LoggedInStackNavigator: {
    screen: keyof LoggedInStackParamList;
    params?: LoggedInStackParamList[keyof LoggedInStackParamList];
  };
  Login: undefined;
  Register: undefined;
};

export const navigationRef = createNavigationContainerRef<RootStackParamList>();

export const linking: LinkingOptions<RootStackParamList> = {
  prefixes: ['https://app.veganna.bg', 'vegannaapp://'],
  config: {
    screens: {
      LoggedInStackNavigator: {
        screens: {
          Recipe: {
            path: 'recipe/:id/:premium',
            parse: {
              id: (id: string) => Number(id),
              premium: (premium: string) => premium === 'true',
            },
          },
        },
      },
    },
  },
  getInitialURL: async () => {
    const url = await Linking.getInitialURL();
    return url;
  },
  subscribe: listener => {
    const subscription = Linking.addEventListener('url', ({ url }) => {
      listener(url);
    });
    return () => subscription.remove();
  },
};
