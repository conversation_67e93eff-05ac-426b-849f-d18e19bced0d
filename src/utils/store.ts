import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { storage } from './storage';

type User = {
  name: string;
};

export type MeasurementSystem = 'metric' | 'imperial';

interface PersistedStore {
  welcome: boolean;
  setPersistedStore: (key: string, value: any) => void;
  user?: User;
  token?: string;
  favourites: { [recipeId: number]: Record<string, any> };
  challengePortions: {
    [challengeId: string]: number;
  };
  hasSeenOnboarding: boolean;
}

interface DataStore {
  challengePortions: {
    [challengeId: string]: number;
  };
  setChallengePortion: (challengeId: number, portions: number) => void;

  system: MeasurementSystem;
  setSystem: (system: MeasurementSystem) => void;

  shouldShowPremiumModal: boolean;
  setShouldShowPremiumModal: (visibility: boolean) => void;
  isUserPremium: boolean;
  setIsUserPremium: (premium: boolean) => void;

  shouldShowAppUpdateScreen: boolean;
  setShouldShowAppUpdateScreen: (visibility: boolean) => void;
  isCriticalUpdate: boolean;
  setIsCriticalUpdate: (isCritical: boolean) => void;
  updateUrl: string;
  setUpdateUrl: (url: string) => void;

  favourites: { [recipeId: number]: Record<string, any> };

  toggleFavourite: ({
    recipeId,
    recipeTitle,
    premium,
  }: {
    recipeId: number;
    recipeTitle: string;
    premium: boolean;
  }) => void;

  ownedBooks: number[];
  setOwnedBooks: (ownedBooks: number[]) => void;
}

export const usePersistedStore = create<PersistedStore>()(
  persist(
    set => ({
      welcome: true,
      favourites: {},
      challengePortions: {},
      user: undefined,
      token: undefined,
      setPersistedStore: (key, value) =>
        set(state => ({ ...state, [key]: value })),
      hasSeenOnboarding: false,
    }),
    {
      name: 'veganna-auth-store',
      storage: createJSONStorage(() => storage),
    },
  ),
);

export const useDataStore = create<DataStore>()((set, get) => ({
  ownedBooks: [],
  challengePortions: usePersistedStore.getState().challengePortions,
  system: 'metric',
  shouldShowPremiumModal: false,
  isUserPremium: false,
  shouldShowAppUpdateScreen: false,
  isCriticalUpdate: false,
  updateUrl: '',
  favourites: usePersistedStore.getState().favourites,

  setOwnedBooks: ownedBooks => set({ ownedBooks }),

  setChallengePortion: (challengeId: number, portions: number) => {
    set(state => ({
      ...state,
      challengePortions: {
        ...state.challengePortions,
        [challengeId]: portions,
      },
    }));

    // update persistance
    usePersistedStore.setState(state => ({
      ...state,
      challengePortions: get().challengePortions,
    }));
  },

  setSystem: system => set(state => ({ ...state, system })),

  setShouldShowPremiumModal: visibility =>
    set(state => ({ ...state, shouldShowPremiumModal: visibility })),

  setIsUserPremium: premium =>
    set(state => ({ ...state, isUserPremium: premium })),

  setShouldShowAppUpdateScreen: visibility =>
    set(state => ({ ...state, shouldShowAppUpdateScreen: visibility })),

  setIsCriticalUpdate: isCritical =>
    set(state => ({ ...state, isCriticalUpdate: isCritical })),

  setUpdateUrl: url => set(state => ({ ...state, updateUrl: url })),

  toggleFavourite({ recipeId, recipeTitle, premium }) {
    const { favourites } = get();
    const updatedFavs = favourites;

    if (Object.keys(updatedFavs).includes(`${recipeId}`)) {
      delete updatedFavs[recipeId];
      set(state => ({
        ...state,
        favourites: {
          ...state.favourites,
          ...updatedFavs,
        },
      }));
    } else {
      set(state => ({
        ...state,
        favourites: {
          ...state.favourites,
          [recipeId]: { recipeTitle, premium },
        },
      }));
    }

    usePersistedStore.setState(state => ({
      ...state,
      favourites: useDataStore.getState().favourites,
    }));
  },
}));
