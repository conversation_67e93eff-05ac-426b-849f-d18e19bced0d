import { axiosInstance } from './api';

export async function updateRecipeViews({ recipeId }: { recipeId: number }) {
  return axiosInstance.post(`/recipes/${recipeId}/view`);
}

export async function validateQuestionnaireAnswers({
  bookId,
  questions,
}: {
  bookId: number;
  questions: {
    question_id: number;
    answer: string;
  }[];
}) {
  return axiosInstance.post(`/books/${bookId}/validate-questions`, {
    questions,
  });
}
