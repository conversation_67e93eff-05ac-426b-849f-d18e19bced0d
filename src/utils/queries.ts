import { QueryClient } from '@tanstack/react-query';
import { Challenge } from '../screens/Challenges';
import { axiosInstance } from './api';
import { logout } from './auth-actions';
import { BookQuestion } from '@features/questionnaires';
import { Recipe } from '@features/recipes';
export interface Product {
  id: number;
  name: string;
  permalink: string;
  images: {
    id: number;
    src: string;
  }[];
  price: string;
}

export type Book = {
  id: number;
  title: string;
  short_title?: string;
  description?: string;
  banner?: string;
  preview?: string;
  position: number;
  is_active: boolean;
  questionnaire_timeout: number;
};

export const queryClient = new QueryClient();

export async function getRecipeById(recipeId: number) {
  return axiosInstance.get('/recipes/' + recipeId).then(res => res.data);
}

export async function me() {
  return axiosInstance
    .get('/me')
    .then(res => res.data)
    .catch(async () => {
      await logout();
    });
}

export async function getCategories({ queryKey }) {
  const tagIds = queryKey[1];

  const params = new URLSearchParams();

  if (tagIds?.length) {
    params.append('tagIds', tagIds.join(','));
  }

  const path = `/categories?` + params.toString();

  return axiosInstance.get(path).then(res => res.data);
}

export async function getTags({ queryKey }) {
  const categoryIds = queryKey[1];

  const params = new URLSearchParams();

  if (categoryIds?.length) {
    params.append('categoryIds', categoryIds.join(','));
  }

  const path = `/tags?` + params.toString();

  return axiosInstance.get(path).then(res => res.data);
}

export async function getChallenges() {
  return axiosInstance.get('/challenges').then(res => res.data?.data);
}

export async function getChallengeById(challengeId: number) {
  return axiosInstance
    .get('/challenges/' + challengeId)
    .then(res => res.data?.data as Challenge);
}

export async function getProducts(): Promise<Product[]> {
  return axiosInstance.get('/products').then(res => res.data);
}

export async function getFavourites(recipeIds: string): Promise<Recipe[]> {
  return axiosInstance
    .get(`/favourites?recipe_ids=${recipeIds}`)
    .then(res => res.data?.data);
}

export async function getHomeData() {
  return axiosInstance.get('/home').then(res => res.data?.data);
}

export async function getBooks(): Promise<Book[]> {
  return axiosInstance.get('/books').then(res => res.data?.data);
}

export async function getBook({ queryKey }): Promise<Book> {
  const bookId = queryKey[1];

  return axiosInstance.get(`/books/${bookId}`).then(res => res.data?.data);
}

export async function getQuestionsByBook({
  queryKey,
}): Promise<BookQuestion[]> {
  const bookId = queryKey[1];

  return axiosInstance
    .get(`/books/${bookId}/questions`)
    .then(res => res.data?.data);
}
