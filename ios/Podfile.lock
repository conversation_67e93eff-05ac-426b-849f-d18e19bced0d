PODS:
  - APNGKit (2.3.0):
    - Delegate (~> 1.3)
  - Base64 (1.1.2)
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - CodePush (8.1.0):
    - Base64 (~> 1.1)
    - JWT (~> 3.0.0-beta.12)
    - React-Core
    - SSZipArchive (~> 2.2.2)
  - Delegate (1.3.0)
  - DoubleConversion (1.1.6)
  - FBAEMKit (17.4.0):
    - FBSDKCoreKit_Basics (= 17.4.0)
  - FBLazyVector (0.72.9)
  - FBReactNativeSpec (0.72.9):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.9)
    - RCTTypeSafety (= 0.72.9)
    - React-Core (= 0.72.9)
    - React-jsi (= 0.72.9)
    - ReactCommon/turbomodule/core (= 0.72.9)
  - FBSDKCoreKit (17.4.0):
    - FBAEMKit (= 17.4.0)
    - FBSDKCoreKit_Basics (= 17.4.0)
  - FBSDKCoreKit_Basics (17.4.0)
  - FBSDKGamingServicesKit (17.4.0):
    - FBSDKCoreKit (= 17.4.0)
    - FBSDKCoreKit_Basics (= 17.4.0)
    - FBSDKShareKit (= 17.4.0)
  - FBSDKLoginKit (17.4.0):
    - FBSDKCoreKit (= 17.4.0)
  - FBSDKShareKit (17.4.0):
    - FBSDKCoreKit (= 17.4.0)
  - Firebase/Analytics (11.5.0):
    - Firebase/Core
  - Firebase/Core (11.5.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.5.0)
  - Firebase/CoreOnly (11.5.0):
    - FirebaseCore (= 11.5.0)
  - Firebase/RemoteConfig (11.5.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.5.0)
  - FirebaseABTesting (11.5.0):
    - FirebaseCore (= 11.5)
  - FirebaseAnalytics (11.5.0):
    - FirebaseAnalytics/AdIdSupport (= 11.5.0)
    - FirebaseCore (= 11.5)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.5.0):
    - FirebaseCore (= 11.5)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.5.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.5.0):
    - FirebaseCoreInternal (= 11.5)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.5.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.5.0):
    - FirebaseCore (= 11.5)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfig (11.5.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (= 11.5)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseRemoteConfigInterop (11.12.0)
  - FirebaseSharedSwift (11.12.0)
  - fmt (6.2.1)
  - Gifu (3.5.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (11.5.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.5.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.5.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.5.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.5.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.72.9):
    - hermes-engine/Pre-built (= 0.72.9)
  - hermes-engine/Pre-built (0.72.9)
  - JWT (3.0.0-beta.14):
    - Base64 (~> 1.1.2)
  - libevent (2.1.12)
  - MMKV (2.2.2):
    - MMKVCore (~> 2.2.2)
  - MMKVCore (2.2.2)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - OneSignalXCFramework (5.2.2):
    - OneSignalXCFramework/OneSignalComplete (= 5.2.2)
  - OneSignalXCFramework/OneSignal (5.2.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalLiveActivities
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalComplete (5.2.2):
    - OneSignalXCFramework/OneSignal
    - OneSignalXCFramework/OneSignalInAppMessages
    - OneSignalXCFramework/OneSignalLocation
  - OneSignalXCFramework/OneSignalCore (5.2.2)
  - OneSignalXCFramework/OneSignalExtension (5.2.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalInAppMessages (5.2.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLiveActivities (5.2.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLocation (5.2.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalNotifications (5.2.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOSCore (5.2.2):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalOutcomes (5.2.2):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalUser (5.2.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
  - PromisesObjC (2.4.0)
  - PurchasesHybridCommon (7.0.0):
    - RevenueCat (= 4.27.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.9)
  - RCTTypeSafety (0.72.9):
    - FBLazyVector (= 0.72.9)
    - RCTRequired (= 0.72.9)
    - React-Core (= 0.72.9)
  - React (0.72.9):
    - React-Core (= 0.72.9)
    - React-Core/DevSupport (= 0.72.9)
    - React-Core/RCTWebSocket (= 0.72.9)
    - React-RCTActionSheet (= 0.72.9)
    - React-RCTAnimation (= 0.72.9)
    - React-RCTBlob (= 0.72.9)
    - React-RCTImage (= 0.72.9)
    - React-RCTLinking (= 0.72.9)
    - React-RCTNetwork (= 0.72.9)
    - React-RCTSettings (= 0.72.9)
    - React-RCTText (= 0.72.9)
    - React-RCTVibration (= 0.72.9)
  - React-callinvoker (0.72.9)
  - React-Codegen (0.72.9):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.9)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.9)
    - React-Core/RCTWebSocket (= 0.72.9)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.9)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.9)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.9):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.9)
    - React-Codegen (= 0.72.9)
    - React-Core/CoreModulesHeaders (= 0.72.9)
    - React-jsi (= 0.72.9)
    - React-RCTBlob
    - React-RCTImage (= 0.72.9)
    - ReactCommon/turbomodule/core (= 0.72.9)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.9):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.9)
    - React-debug (= 0.72.9)
    - React-jsi (= 0.72.9)
    - React-jsinspector (= 0.72.9)
    - React-logger (= 0.72.9)
    - React-perflogger (= 0.72.9)
    - React-runtimeexecutor (= 0.72.9)
  - React-debug (0.72.9)
  - React-hermes (0.72.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.9)
    - React-jsi
    - React-jsiexecutor (= 0.72.9)
    - React-jsinspector (= 0.72.9)
    - React-perflogger (= 0.72.9)
  - React-jsi (0.72.9):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.9)
    - React-jsi (= 0.72.9)
    - React-perflogger (= 0.72.9)
  - React-jsinspector (0.72.9)
  - React-logger (0.72.9):
    - glog
  - react-native-fbsdk-next (13.1.3):
    - React-Core
    - react-native-fbsdk-next/Core (= 13.1.3)
    - react-native-fbsdk-next/Login (= 13.1.3)
    - react-native-fbsdk-next/Share (= 13.1.3)
  - react-native-fbsdk-next/Core (13.1.3):
    - FBSDKCoreKit (~> 17.3)
    - React-Core
  - react-native-fbsdk-next/Login (13.1.3):
    - FBSDKLoginKit (~> 17.3)
    - React-Core
  - react-native-fbsdk-next/Share (13.1.3):
    - FBSDKGamingServicesKit (~> 17.3)
    - FBSDKShareKit (~> 17.3)
    - React-Core
  - react-native-mmkv (2.10.2):
    - MMKV (>= 1.2.13)
    - React-Core
  - react-native-netinfo (9.4.1):
    - React-Core
  - react-native-onesignal (5.2.2):
    - OneSignalXCFramework (= 5.2.2)
    - React (< 1.0.0, >= 0.13.0)
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (4.7.2):
    - React-Core
  - react-native-screenguard (1.0.4):
    - React-Core
    - SDWebImage (~> 5.19.4)
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-turbo-image (1.22.3):
    - APNGKit
    - Gifu
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - SwiftSVG
  - React-NativeModulesApple (0.72.9):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.9)
  - React-RCTActionSheet (0.72.9):
    - React-Core/RCTActionSheetHeaders (= 0.72.9)
  - React-RCTAnimation (0.72.9):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.9)
    - React-Codegen (= 0.72.9)
    - React-Core/RCTAnimationHeaders (= 0.72.9)
    - React-jsi (= 0.72.9)
    - ReactCommon/turbomodule/core (= 0.72.9)
  - React-RCTAppDelegate (0.72.9):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.9):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.9)
    - React-Core/RCTBlobHeaders (= 0.72.9)
    - React-Core/RCTWebSocket (= 0.72.9)
    - React-jsi (= 0.72.9)
    - React-RCTNetwork (= 0.72.9)
    - ReactCommon/turbomodule/core (= 0.72.9)
  - React-RCTImage (0.72.9):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.9)
    - React-Codegen (= 0.72.9)
    - React-Core/RCTImageHeaders (= 0.72.9)
    - React-jsi (= 0.72.9)
    - React-RCTNetwork (= 0.72.9)
    - ReactCommon/turbomodule/core (= 0.72.9)
  - React-RCTLinking (0.72.9):
    - React-Codegen (= 0.72.9)
    - React-Core/RCTLinkingHeaders (= 0.72.9)
    - React-jsi (= 0.72.9)
    - ReactCommon/turbomodule/core (= 0.72.9)
  - React-RCTNetwork (0.72.9):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.9)
    - React-Codegen (= 0.72.9)
    - React-Core/RCTNetworkHeaders (= 0.72.9)
    - React-jsi (= 0.72.9)
    - ReactCommon/turbomodule/core (= 0.72.9)
  - React-RCTSettings (0.72.9):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.9)
    - React-Codegen (= 0.72.9)
    - React-Core/RCTSettingsHeaders (= 0.72.9)
    - React-jsi (= 0.72.9)
    - ReactCommon/turbomodule/core (= 0.72.9)
  - React-RCTText (0.72.9):
    - React-Core/RCTTextHeaders (= 0.72.9)
  - React-RCTVibration (0.72.9):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.9)
    - React-Core/RCTVibrationHeaders (= 0.72.9)
    - React-jsi (= 0.72.9)
    - ReactCommon/turbomodule/core (= 0.72.9)
  - React-rncore (0.72.9)
  - React-runtimeexecutor (0.72.9):
    - React-jsi (= 0.72.9)
  - React-runtimescheduler (0.72.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.9):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.9)
    - React-cxxreact (= 0.72.9)
    - React-jsi (= 0.72.9)
    - React-logger (= 0.72.9)
    - React-perflogger (= 0.72.9)
  - ReactCommon/turbomodule/core (0.72.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.9)
    - React-cxxreact (= 0.72.9)
    - React-jsi (= 0.72.9)
    - React-logger (= 0.72.9)
    - React-perflogger (= 0.72.9)
  - RevenueCat (4.27.0)
  - RNCMaskedView (0.2.9):
    - React-Core
  - RNDeviceInfo (10.9.0):
    - React-Core
  - RNFBAnalytics (21.6.1):
    - Firebase/Analytics (= 11.5.0)
    - React-Core
    - RNFBApp
  - RNFBApp (21.6.1):
    - Firebase/CoreOnly (= 11.5.0)
    - React-Core
  - RNFBRemoteConfig (21.6.1):
    - Firebase/RemoteConfig (= 11.5.0)
    - React-Core
    - RNFBApp
  - RNFlashList (1.7.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNPurchases (7.0.0):
    - PurchasesHybridCommon (= 7.0.0)
    - React-Core
  - RNReanimated (3.5.1):
    - DoubleConversion
    - FBLazyVector
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTAppDelegate
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.25.0):
    - React-Core
    - React-RCTImage
  - RNSVG (13.13.0):
    - React-Core
  - SDWebImage (5.19.7):
    - SDWebImage/Core (= 5.19.7)
  - SDWebImage/Core (5.19.7)
  - SocketRocket (0.6.1)
  - SSZipArchive (2.2.3)
  - SwiftSVG (2.3.2)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - CodePush (from `../node_modules/react-native-code-push`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase/Core
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - OneSignalXCFramework (< 6.0, >= 5.0.0)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-fbsdk-next (from `../node_modules/react-native-fbsdk-next`)
  - react-native-mmkv (from `../node_modules/react-native-mmkv`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-onesignal (from `../node_modules/react-native-onesignal`)
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-screenguard (from `../node_modules/react-native-screenguard`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-turbo-image (from `../node_modules/react-native-turbo-image`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBRemoteConfig (from `../node_modules/@react-native-firebase/remote-config`)"
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNPurchases (from `../node_modules/react-native-purchases`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - APNGKit
    - Base64
    - Delegate
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKGamingServicesKit
    - FBSDKLoginKit
    - FBSDKShareKit
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSharedSwift
    - fmt
    - Gifu
    - GoogleAppMeasurement
    - GoogleUtilities
    - JWT
    - libevent
    - MMKV
    - MMKVCore
    - nanopb
    - OneSignalXCFramework
    - PromisesObjC
    - PurchasesHybridCommon
    - RevenueCat
    - SDWebImage
    - SocketRocket
    - SSZipArchive
    - SwiftSVG

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  CodePush:
    :path: "../node_modules/react-native-code-push"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-fbsdk-next:
    :path: "../node_modules/react-native-fbsdk-next"
  react-native-mmkv:
    :path: "../node_modules/react-native-mmkv"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-onesignal:
    :path: "../node_modules/react-native-onesignal"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-screenguard:
    :path: "../node_modules/react-native-screenguard"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-turbo-image:
    :path: "../node_modules/react-native-turbo-image"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBRemoteConfig:
    :path: "../node_modules/@react-native-firebase/remote-config"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNPurchases:
    :path: "../node_modules/react-native-purchases"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  APNGKit: eb7e111277527cfd47636f797c9c8e7aab5d9601
  Base64: cecfb41a004124895a7bcee567a89bae5a89d49b
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  CodePush: 255d7607f428dcbec39085f81a206b170e01d012
  Delegate: 0ff4467868095239ff578ab531efd8af46e62881
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBAEMKit: 58cb5f302cdd715a56d4c1d0dfdd2e423ac1421a
  FBLazyVector: dc178b8748748c036ef9493a5d59d6d1f91a36ce
  FBReactNativeSpec: d0aaae78e93c89dc2d691d8052a4d2aeb1b461ee
  FBSDKCoreKit: 94d7461d0cecf441b1ba7c41acfff41daa8ccd41
  FBSDKCoreKit_Basics: 151b43db8b834d3f0e02f95d36a44ffd36265e45
  FBSDKGamingServicesKit: 55a13febe0cd117bf5f8f61f315d75aab9b7876e
  FBSDKLoginKit: 5c1cd53c91a2282b3a4fe6e6d3dcf2b8b0d33d55
  FBSDKShareKit: 00546a02dce72f37a4209cd68aaf5fb749185c3b
  Firebase: 7a56fe4f56b5ab81b86a6822f5b8f909ae6fc7e2
  FirebaseABTesting: 42403a7ffdde1904cb063b5bc2d27dd200e37ac2
  FirebaseAnalytics: 2f4a11eeb7a0e9c6fcf642d4e6aaca7fa4d38c28
  FirebaseCore: 93abc05437f8064cd2bc0a53b768fb0bc5a1d006
  FirebaseCoreInternal: f47dd28ae7782e6a4738aad3106071a8fe0af604
  FirebaseInstallations: d8063d302a426d114ac531cd82b1e335a0565745
  FirebaseRemoteConfig: 9c06ced90c1561c18ccfc258e2548371eb3a7137
  FirebaseRemoteConfigInterop: 82b81fd06ee550cbeff40004e2c106daedf73e38
  FirebaseSharedSwift: d2475748a2d2a36242ed13baa34b2acda846c925
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  Gifu: 9f7e52357d41c0739709019eb80a71ad9aab1b6d
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleAppMeasurement: ee5c2d2242816773fbf79e5b0563f5355ef1c315
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  hermes-engine: 9b9bb14184a11b8ceb4131b09abf634880f0f46d
  JWT: ef71dfb03e1f842081e64dc42eef0e164f35d251
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  MMKV: b4802ebd5a7c68fc0c4a5ccb4926fbdfb62d68e0
  MMKVCore: a255341a3746955f50da2ad9121b18cb2b346e61
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  OneSignalXCFramework: f06edd9b146c7ac5935136a117ce2a5fdd6420f6
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PurchasesHybridCommon: af3b2413f9cb999bc1fdca44770bdaf39dfb89fa
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: f30c3213569b1dc43659ecc549a6536e1e11139e
  RCTTypeSafety: e1ed3137728804fa98bce30b70e3da0b8e23054e
  React: 54070abee263d5773486987f1cf3a3616710ed52
  React-callinvoker: 794ea19cc4d8ce25921893141e131b9d6b7d02eb
  React-Codegen: 10359be5377b1a652839bcfe7b6b5bd7f73ae9f6
  React-Core: 7e2a9c4594083ecc68b91fc4a3f4d567e8c8b3b3
  React-CoreModules: 87cc386c2200862672b76bb02c4574b4b1d11b3c
  React-cxxreact: 1100498800597e812f0ce4ec365f4ea47ac39719
  React-debug: 4dca41301a67ab2916b2c99bef60344a7b653ac5
  React-hermes: b871a77ba1c427ca00f075759dc0cc9670484c94
  React-jsi: 1f8d073a00264c6a701c4b7b4f4ef9946f9b2455
  React-jsiexecutor: 5a169b1dd1abad06bed40ab7e1aca883c657d865
  React-jsinspector: 54205b269da20c51417e0fc02c4cde9f29a4bf1a
  React-logger: f42d2f2bc4cbb5d19d7c0ce84b8741b1e54e88c8
  react-native-fbsdk-next: 0326e3f3892b3f1cb66bfb884f289e2bb818ee89
  react-native-mmkv: 9ae7ca3977e8ef48dbf7f066974eb844c20b5fd7
  react-native-netinfo: fefd4e98d75cbdd6e85fc530f7111a8afdf2b0c5
  react-native-onesignal: 592184d34780d04ebbea2b9844d5b6088606cb30
  react-native-render-html: 984dfe2294163d04bf5fe25d7c9f122e60e05ebe
  react-native-safe-area-context: 7aa8e6d9d0f3100a820efb1a98af68aa747f9284
  react-native-screenguard: 347f6a5d1cefbd5cd25034d41ba4c6d5088443d7
  react-native-splash-screen: 4312f786b13a81b5169ef346d76d33bc0c6dc457
  react-native-turbo-image: 8fe6d44994e294906e9c1a580952bd16278a908c
  React-NativeModulesApple: 9f72feb8a04020b32417f768a7e1e40eec91fef4
  React-perflogger: cb433f318c6667060fc1f62e26eb58d6eb30a627
  React-RCTActionSheet: 0af3f8ac067e8a1dde902810b7ad169d0a0ec31e
  React-RCTAnimation: 453a88e76ba6cb49819686acd8b21ce4d9ee4232
  React-RCTAppDelegate: b9fb07959f227ddd2c458c42ed5ceacbd1e1e367
  React-RCTBlob: fa513d56cdc2b7ad84a7758afc4863c1edd6a8b1
  React-RCTImage: 8e059fbdfab18b86127424dc3742532aab960760
  React-RCTLinking: 05ae2aa525b21a7f1c5069c14330700f470efd97
  React-RCTNetwork: 7ed9d99d028c53e9a23e318f65937f499ba8a6fd
  React-RCTSettings: 8b12ebf04d4baa0e259017fcef6cf7abd7d8ac51
  React-RCTText: a062ade9ff1591c46bcb6c5055fd4f96c154b8aa
  React-RCTVibration: 87c490b6f01746ab8f9b4e555f514cc030c06731
  React-rncore: 140bc11b316da7003bf039844aef39e1c242d7ad
  React-runtimeexecutor: 226ebef5f625878d3028b196cbecbbdeb6f208e4
  React-runtimescheduler: a7b1442e155c6f131d8bdfaac47abdc303f50788
  React-utils: a3ffbc321572ee91911d7bc30965abe9aa4e16af
  ReactCommon: 180205f326d59f52e12fa724f5278fcf8fb6afc3
  RevenueCat: 84fbe2eb9bbf63e1abf346ccd3ff9ee45d633e3b
  RNCMaskedView: 949696f25ec596bfc697fc88e6f95cf0c79669b6
  RNDeviceInfo: 02ea8b23e2280fa18e00a06d7e62804d74028579
  RNFBAnalytics: 2db198b84b1809770b1ddfafd1113f351ac459d1
  RNFBApp: 1de631bffe154f4f8b56f8dbfb13d6305d658e97
  RNFBRemoteConfig: 9558ac5d11d4da5a36f939a5e3ca5652413d059e
  RNFlashList: fadf593d8e195ce2fe261980ac59e9f49808b68c
  RNPurchases: d146276420502eecb798e4cf49078aec3ee4ee6e
  RNReanimated: 99aa8c96151abbc2d7e737a56ec62aca709f0c92
  RNScreens: 85d3880b52d34db7b8eeebe2f1a0e807c05e69fa
  RNSVG: ed492aaf3af9ca01bc945f7a149d76d62e73ec82
  SDWebImage: 8a6b7b160b4d710e2a22b6900e25301075c34cb3
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  SSZipArchive: 62d4947b08730e4cda640473b0066d209ff033c9
  SwiftSVG: ca7f64e403b8ea7ea8eba8c823b83ba4a059834c
  Yoga: eddf2bbe4a896454c248a8f23b4355891eb720a6

PODFILE CHECKSUM: 4a96f590abd4573d353160514c52db09e370d3da

COCOAPODS: 1.16.2
