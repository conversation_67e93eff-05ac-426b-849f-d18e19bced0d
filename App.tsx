import NetInfo from '@react-native-community/netinfo';
import analytics from '@react-native-firebase/analytics';
import { NavigationContainer } from '@react-navigation/native';
import {
  QueryClientProvider,
  focusManager,
  onlineManager,
} from '@tanstack/react-query';
import React from 'react';
import type { AppStateStatus } from 'react-native';
import { AppState, Platform, StatusBar } from 'react-native';
import { OneSignal } from 'react-native-onesignal';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import SplashScreen from 'react-native-splash-screen';
import Toast from 'react-native-toast-message';

import { toastConfig } from '@components/Toast';
import { updateSubscriptionStatusManually } from '@utils/api';
import { checkForUpdates } from '@utils/app-updates';
import { initFirebaseRemoteConfig } from '@utils/firebase-remote-config';
import { setupScreenGuard } from '@utils/screen-guard';
import { useDataStore, usePersistedStore } from '@utils/store';
import EntryStackNavigator from './src/components/EntryStackNavigator';
import PremiumModal from './src/components/PremiumModal';
import AppUpdate from './src/screens/AppUpdate';
import UpdatingApp from './src/screens/UpdatingApp';
import { useCodePush, useIAP } from './src/utils/hooks';
import { linking, navigationRef } from './src/utils/navigation';
import { queryClient } from './src/utils/queries';

onlineManager.setEventListener(setOnline => {
  return NetInfo.addEventListener(state => {
    setOnline(!!state.isConnected);
  });
});

const App = () => {
  useIAP();
  const isUserPremium = useDataStore(state => state.isUserPremium);
  const token = usePersistedStore(state => state.token);

  // OneSignal Initialization
  OneSignal.initialize('************************************');

  const { isAppUpdating } = useCodePush();

  React.useEffect(() => {
    SplashScreen.hide();
    initFirebaseRemoteConfig();
    checkForUpdates();
    analytics().logEvent('app_launched');
  }, []);

  // Executes on login/register, and on app launch with active session
  React.useEffect(() => {
    const onActiveSession = async () => {
      if (token) {
        await updateSubscriptionStatusManually(token, isUserPremium);

        await setupScreenGuard();
      }
    };

    onActiveSession();
  }, [token]);

  React.useEffect(() => {
    function onAppStateChange(status: AppStateStatus) {
      if (Platform.OS !== 'web') {
        focusManager.setFocused(status === 'active');
      }
    }

    const subscription = AppState.addEventListener('change', onAppStateChange);

    return () => subscription.remove();
  }, []);

  if (isAppUpdating) return <UpdatingApp />;

  return (
    <QueryClientProvider client={queryClient}>
      <SafeAreaProvider>
        <StatusBar hidden={true} />

        <NavigationContainer ref={navigationRef} linking={linking}>
          <EntryStackNavigator />

          <AppUpdate />
          <PremiumModal />
        </NavigationContainer>

        <Toast config={toastConfig} />
      </SafeAreaProvider>
    </QueryClientProvider>
  );
};

export default App;
