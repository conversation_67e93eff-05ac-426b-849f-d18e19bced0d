# Veganna App

Recipe app for Anna Veganna.

## Tech stack

- react native
- zustand
- twrnc
- mmkv 
- react-navigation
- code push
- revenue cat
- onesignal


## Build and deployment

Prerequisites: 
- **You should configure your production keys for App Center in and install the appcenter cli before trying to push a new build.**

- You need to setup your Xcode account and profiles (iOS) and paste the `veganna-google-play.keystore` file in `android/app` (Android). This step is not needed for code push builds.


1. Use `node 18`

2. If you want to publish a new code push version, run `npm push:android` for Android and `npm push:ios` for iOS. Note that the new versions are bound to the versions in the stores. Read more here https://github.com/microsoft/react-native-code-push

